#!/usr/bin/env python3
"""
从CSV文件提取邮箱地址的工具
"""

import csv
import sys
from pathlib import Path

def extract_emails_from_csv(csv_file, email_column, output_file):
    """从CSV文件提取邮箱地址"""
    
    emails = []
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            # 尝试自动检测分隔符
            sample = f.read(1024)
            f.seek(0)
            
            # 检测分隔符
            sniffer = csv.Sniffer()
            delimiter = sniffer.sniff(sample).delimiter
            
            reader = csv.DictReader(f, delimiter=delimiter)
            
            for row in reader:
                if email_column in row and row[email_column]:
                    email = row[email_column].strip()
                    if email and '@' in email:
                        emails.append(email)
    
    except Exception as e:
        print(f"读取CSV文件失败: {e}")
        return False
    
    # 去重
    unique_emails = list(set(emails))
    
    # 保存到文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(unique_emails))
        
        print(f"✓ 成功提取 {len(unique_emails)} 个唯一邮箱地址")
        print(f"✓ 已保存到: {output_file}")
        return True
        
    except Exception as e:
        print(f"保存文件失败: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) != 4:
        print("使用方法:")
        print("python csv_to_emails.py <CSV文件> <邮箱列名> <输出文件>")
        print("\n示例:")
        print("python csv_to_emails.py data.csv email emails.txt")
        print("python csv_to_emails.py users.csv 邮箱地址 email_list.txt")
        return
    
    csv_file = sys.argv[1]
    email_column = sys.argv[2]
    output_file = sys.argv[3]
    
    if not Path(csv_file).exists():
        print(f"错误: CSV文件不存在: {csv_file}")
        return
    
    print(f"从 {csv_file} 提取邮箱地址...")
    print(f"邮箱列名: {email_column}")
    print(f"输出文件: {output_file}")
    
    success = extract_emails_from_csv(csv_file, email_column, output_file)
    
    if success:
        print(f"\n下一步: 使用以下命令进行批量验证")
        print(f"python main.py batch {output_file} -o results.json --use-proxy")

if __name__ == "__main__":
    main()
