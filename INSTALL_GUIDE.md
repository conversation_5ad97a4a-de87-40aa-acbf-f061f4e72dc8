# 安装和配置指南

## 📦 环境准备

### 1. Python环境
确保你的系统已安装Python 3.7或更高版本：

```bash
python --version
```

如果没有安装Python，请从 [python.org](https://www.python.org/) 下载安装。

### 2. 下载项目
```bash
git clone <repository-url>
cd email-validator
```

或者直接下载ZIP文件并解压。

## 🔧 安装步骤

### 步骤1：安装Python依赖

```bash
pip install -r requirements.txt
```

如果遇到权限问题，可以使用：
```bash
pip install --user -r requirements.txt
```

### 步骤2：初始化项目配置

```bash
python main.py setup
```

这个命令会：
- 创建必要的目录结构
- 检查配置文件
- 创建示例文件

### 步骤3：配置代理（重要）

1. 打开 `config/proxies.txt` 文件
2. 按照以下格式添加你的代理：

```
# 代理格式：host:port:username:password
global.rotgb.711proxy.com:10000:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10001:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10002:USER456549-zone-custom:ec5512
```

**重要提示**：
- 每行一个代理
- 格式必须严格按照 `host:port:username:password`
- 不要有多余的空格
- 以 `#` 开头的行是注释

### 步骤4：配置环境变量（可选）

复制 `.env.example` 为 `.env`：
```bash
cp .env.example .env
```

编辑 `.env` 文件，根据需要修改配置：
```bash
# 代理配置
PROXY_FILE=config/proxies.txt
PROXY_ENABLED=true

# 日志级别
LOG_LEVEL=INFO

# 并发设置
MAX_WORKERS=50
```

## ✅ 验证安装

### 1. 检查代理状态
```bash
python main.py proxy-status
```

应该看到类似输出：
```
✓ Loaded 3/3 working proxies
=== Proxy Status ===
Total Proxies: 3
Working Proxies: 3
Failed Proxies: 0
```

### 2. 测试单个邮箱验证
```bash
python main.<NAME_EMAIL>
```

### 3. 测试批量验证
```bash
python main.py batch data/sample_emails.txt
```

## 🔧 配置详解

### 代理配置详解

你的代理格式是：`global.rotgb.711proxy.com:10000:USER456549-zone-custom:ec5512`

这表示：
- **主机**: global.rotgb.711proxy.com
- **端口**: 10000
- **用户名**: USER456549-zone-custom
- **密码**: ec5512

在 `config/proxies.txt` 中添加多个代理：
```
global.rotgb.711proxy.com:10000:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10001:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10002:USER456549-zone-custom:ec5512
# 添加更多代理...
```

### 邮箱域名配置

`config/email_domains.json` 包含日本三大运营商的配置：

```json
{
  "japan_carriers": {
    "docomo": {
      "domains": ["docomo.ne.jp"],
      "smtp_servers": ["mail.docomo.ne.jp", "smtp.docomo.ne.jp"]
    },
    "au_kddi": {
      "domains": ["au.com", "ezweb.ne.jp", "uqmobile.jp"],
      "smtp_servers": ["mail.au.com", "smtp.ezweb.ne.jp"]
    },
    "softbank": {
      "domains": ["softbank.ne.jp", "i.softbank.jp", "vodafone.ne.jp"],
      "smtp_servers": ["mail.softbank.ne.jp", "smtp.softbank.ne.jp"]
    }
  }
}
```

### 全局设置配置

`config/settings.json` 控制验证行为：

```json
{
  "general": {
    "max_concurrent_checks": 50,
    "request_delay": 0.5,
    "retry_attempts": 3,
    "timeout": 30
  },
  "proxy": {
    "enabled": true,
    "rotation_interval": 10,
    "health_check_interval": 300,
    "max_failures": 5
  },
  "validation": {
    "check_syntax": true,
    "check_mx_record": true,
    "check_smtp": true,
    "deep_validation": true
  }
}
```

## 🚀 使用示例

### 基本使用

1. **验证单个邮箱**：
```bash
python main.<NAME_EMAIL> --use-proxy
```

2. **批量验证**：
```bash
python main.py batch emails.txt -o results.json --use-proxy -w 20
```

3. **检查代理状态**：
```bash
python main.py proxy-status
```

### 高级使用

1. **自定义代理文件**：
```bash
python main.<NAME_EMAIL> --proxy-file my_proxies.txt --use-proxy
```

2. **调整并发数**：
```bash
python main.py batch large_list.txt -w 50 --use-proxy
```

3. **指定输出文件**：
```bash
python main.py batch emails.txt -o detailed_results.json
```

## 📁 目录结构说明

安装完成后，你的目录结构应该是：

```
email-validator/
├── main.py                 # 主程序
├── requirements.txt        # 依赖列表
├── README.md              # 使用说明
├── INSTALL_GUIDE.md       # 安装指南
├── .env.example           # 环境变量示例
├── config/                # 配置目录
│   ├── proxies.txt        # 代理配置 ⭐
│   ├── email_domains.json # 域名配置
│   └── settings.json      # 全局设置
├── src/                   # 源代码
│   ├── email_validator.py
│   ├── proxy_manager.py
│   └── japan_carriers.py
├── logs/                  # 日志文件
├── results/               # 验证结果
└── data/                  # 数据文件
    └── sample_emails.txt  # 示例邮箱
```

## 🐛 常见安装问题

### 问题1：pip安装失败
```bash
# 解决方案1：升级pip
python -m pip install --upgrade pip

# 解决方案2：使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 问题2：权限错误
```bash
# Windows
pip install --user -r requirements.txt

# Linux/Mac
sudo pip install -r requirements.txt
```

### 问题3：代理连接失败
1. 检查代理格式是否正确
2. 确认代理服务器可用
3. 验证用户名密码正确

### 问题4：DNS解析错误
1. 检查网络连接
2. 尝试使用不同的DNS服务器
3. 确认防火墙设置

## 📞 获取帮助

如果遇到安装问题：

1. 检查 `logs/email_validator.log` 日志文件
2. 确认Python版本兼容性
3. 验证所有依赖包已正确安装
4. 检查网络连接和代理设置

## 🎯 下一步

安装完成后，建议：

1. 阅读 `README.md` 了解详细使用方法
2. 测试代理连接和邮箱验证功能
3. 根据需要调整配置参数
4. 准备要验证的邮箱列表
