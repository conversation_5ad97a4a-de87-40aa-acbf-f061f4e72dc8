#!/usr/bin/env python3
"""
项目打包脚本
创建完整的邮件验证工具包
"""

import os
import zipfile
import shutil
from pathlib import Path
import datetime

def create_project_package():
    """创建项目压缩包"""
    
    # 项目名称和版本
    project_name = "email-validator-japan"
    version = datetime.datetime.now().strftime("%Y%m%d")
    package_name = f"{project_name}-v{version}.zip"
    
    print(f"创建项目包: {package_name}")
    
    # 需要包含的文件和目录
    files_to_include = [
        # 主要文件
        "main.py",
        "requirements.txt", 
        "README.md",
        "INSTALL_GUIDE.md",
        "USAGE_EXAMPLES.md",
        "run_example.py",
        ".env.example",
        
        # 源代码目录
        "src/__init__.py",
        "src/email_validator.py",
        "src/proxy_manager.py", 
        "src/japan_carriers.py",
        
        # 配置文件
        "config/proxies.txt",
        "config/email_domains.json",
        "config/settings.json"
    ]
    
    # 创建临时目录
    temp_dir = Path("temp_package")
    if temp_dir.exists():
        shutil.rmtree(temp_dir)
    temp_dir.mkdir()
    
    # 复制文件到临时目录
    for file_path in files_to_include:
        src_path = Path(file_path)
        if src_path.exists():
            # 创建目标目录
            dst_path = temp_dir / file_path
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            shutil.copy2(src_path, dst_path)
            print(f"✓ 已添加: {file_path}")
        else:
            print(f"✗ 文件不存在: {file_path}")
    
    # 创建必要的空目录
    empty_dirs = ["logs", "results", "data"]
    for dir_name in empty_dirs:
        dir_path = temp_dir / dir_name
        dir_path.mkdir(exist_ok=True)
        
        # 创建.gitkeep文件保持目录
        gitkeep_file = dir_path / ".gitkeep"
        gitkeep_file.write_text("# 保持目录结构\n")
        print(f"✓ 已创建目录: {dir_name}")
    
    # 创建项目信息文件
    project_info = f"""# 邮件验证工具 - 日本运营商专版

版本: v{version}
创建时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 快速开始

1. 解压文件到你的工作目录
2. 安装依赖: pip install -r requirements.txt
3. 初始化配置: python main.py setup
4. 配置代理: 编辑 config/proxies.txt
5. 开始验证: python main.<NAME_EMAIL> --use-proxy

## 文件说明

- main.py - 主程序入口
- requirements.txt - Python依赖包
- README.md - 详细使用说明
- INSTALL_GUIDE.md - 安装配置指南
- USAGE_EXAMPLES.md - 使用示例
- run_example.py - 示例脚本
- config/ - 配置文件目录
- src/ - 源代码目录
- logs/ - 日志文件目录
- results/ - 验证结果目录
- data/ - 数据文件目录

## 支持的日本运营商

- DoCoMo (docomo.ne.jp)
- au/KDDI (au.com, ezweb.ne.jp, uqmobile.jp)
- SoftBank (softbank.ne.jp, i.softbank.jp, vodafone.ne.jp)

祝使用愉快！
"""
    
    info_file = temp_dir / "PROJECT_INFO.txt"
    info_file.write_text(project_info, encoding='utf-8')
    print("✓ 已创建项目信息文件")
    
    # 创建ZIP压缩包
    with zipfile.ZipFile(package_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                file_path = Path(root) / file
                # 计算相对路径
                arcname = file_path.relative_to(temp_dir)
                zipf.write(file_path, arcname)
                print(f"✓ 已压缩: {arcname}")
    
    # 清理临时目录
    shutil.rmtree(temp_dir)
    
    # 显示结果
    package_size = os.path.getsize(package_name) / 1024  # KB
    print(f"\n🎉 项目包创建成功!")
    print(f"📦 文件名: {package_name}")
    print(f"📏 大小: {package_size:.1f} KB")
    print(f"📍 位置: {os.path.abspath(package_name)}")
    
    return package_name

def create_download_instructions():
    """创建下载说明"""
    instructions = """
# 📥 项目下载说明

## 方法1: 直接下载压缩包

运行打包脚本创建压缩包:
```bash
python create_package.py
```

然后下载生成的 .zip 文件。

## 方法2: 手动创建项目结构

如果无法下载压缩包，可以手动创建以下目录结构:

```
email-validator/
├── main.py
├── requirements.txt
├── README.md
├── INSTALL_GUIDE.md
├── USAGE_EXAMPLES.md
├── run_example.py
├── .env.example
├── config/
│   ├── proxies.txt
│   ├── email_domains.json
│   └── settings.json
├── src/
│   ├── __init__.py
│   ├── email_validator.py
│   ├── proxy_manager.py
│   └── japan_carriers.py
├── logs/
├── results/
└── data/
```

然后逐个复制文件内容。

## 方法3: 使用Git (如果有仓库)

```bash
git clone <repository-url>
cd email-validator
```

## 安装和使用

1. 解压或创建项目目录
2. 进入项目目录: `cd email-validator`
3. 安装依赖: `pip install -r requirements.txt`
4. 初始化配置: `python main.py setup`
5. 配置代理: 编辑 `config/proxies.txt`
6. 开始使用: `python main.<NAME_EMAIL> --use-proxy`

## 重要配置

### 代理配置 (config/proxies.txt)
```
global.rotgb.711proxy.com:10000:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10001:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10002:USER456549-zone-custom:ec5512
```

### 环境变量 (.env)
复制 .env.example 为 .env 并根据需要修改。

如有问题，请参考 INSTALL_GUIDE.md 详细安装指南。
"""
    
    with open("DOWNLOAD_INSTRUCTIONS.md", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✓ 已创建下载说明文件: DOWNLOAD_INSTRUCTIONS.md")

if __name__ == "__main__":
    print("邮件验证工具 - 项目打包器")
    print("=" * 50)
    
    try:
        # 创建项目包
        package_name = create_project_package()
        
        # 创建下载说明
        create_download_instructions()
        
        print("\n" + "=" * 50)
        print("✅ 打包完成!")
        print(f"\n📦 你的项目包: {package_name}")
        print("📖 下载说明: DOWNLOAD_INSTRUCTIONS.md")
        print("\n🚀 下一步:")
        print("1. 下载压缩包到你的本地电脑")
        print("2. 解压到工作目录")
        print("3. 按照 INSTALL_GUIDE.md 进行配置")
        print("4. 开始验证邮箱!")
        
    except Exception as e:
        print(f"❌ 打包失败: {e}")
        import traceback
        traceback.print_exc()
