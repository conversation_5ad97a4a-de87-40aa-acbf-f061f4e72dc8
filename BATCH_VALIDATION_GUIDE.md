# 📊 批量验证完整指南

## 🎯 概述

批量验证允许你一次性验证大量邮箱地址，支持从几十个到数万个邮箱的高效处理。

## 📝 步骤1: 准备邮箱列表文件

### 方法1: 手动创建文本文件（最简单）

创建一个 `.txt` 文件，每行一个邮箱：

```
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
```

**文件格式要求**：
- 每行一个邮箱地址
- 使用UTF-8编码
- 可以包含空行（会被忽略）
- 可以用 `#` 开头添加注释

### 方法2: 从Excel导出

1. **在Excel中**：
   - 选择包含邮箱的列
   - 复制 (Ctrl+C)
   - 打开记事本
   - 粘贴 (Ctrl+V)
   - 保存为 `.txt` 文件

2. **从CSV文件提取**：
```bash
# 使用提供的工具
python tools/csv_to_emails.py data.csv email_column emails.txt
```

### 方法3: 使用邮箱列表管理工具

```bash
# 交互式模式
python tools/email_list_manager.py

# 创建示例列表
python tools/email_list_manager.py sample 100 my_emails.txt

# 清理现有列表
python tools/email_list_manager.py clean raw_emails.txt clean_emails.txt
```

## 🚀 步骤2: 执行批量验证

### 基本批量验证

```bash
python main.py batch emails.txt
```

### 使用代理的批量验证（推荐）

```bash
python main.py batch emails.txt --use-proxy
```

### 高级批量验证选项

```bash
# 完整命令示例
python main.py batch emails.txt \
  --use-proxy \
  --output results.json \
  --workers 20 \
  --proxy-file config/proxies.txt
```

**参数说明**：
- `emails.txt` - 邮箱列表文件
- `--use-proxy` - 使用代理进行验证
- `--output results.json` - 指定结果输出文件
- `--workers 20` - 并发线程数（默认10）
- `--proxy-file` - 指定代理文件路径

## ⚙️ 步骤3: 优化验证参数

### 并发数设置建议

```bash
# 小规模验证 (< 1000个邮箱)
python main.py batch emails.txt --use-proxy -w 5

# 中等规模验证 (1000-10000个邮箱)
python main.py batch emails.txt --use-proxy -w 20

# 大规模验证 (> 10000个邮箱)
python main.py batch emails.txt --use-proxy -w 50
```

### 分批处理大型列表

对于超大型邮箱列表，建议分批处理：

```bash
# 使用工具分割大文件
python tools/email_list_manager.py

# 选择选项5，将大文件分割成小文件
# 然后分别验证每个小文件
```

## 📊 步骤4: 查看验证结果

### 实时进度显示

验证过程中会显示：
```
✓ Loaded 3/3 working proxies
Found 1000 emails to validate
Validating emails: 100%|████████████| 1000/1000 [05:23<00:00, 3.09it/s]
✓ Results saved to: results/validation_results.json

=== Validation Summary ===
Total emails: 1000
Valid: 756
Invalid: 201
Errors: 43

=== Carrier Statistics ===
┌─────────┬───────┬───────┬──────────────┐
│ Carrier │ Total │ Valid │ Success Rate │
├─────────┼───────┼───────┼──────────────┤
│ DOCOMO  │  334  │  298  │    89.2%     │
│ AU_KDDI │  289  │  245  │    84.8%     │
│ SOFTBANK│  312  │  213  │    68.3%     │
│ UNKNOWN │   65  │   0   │     0.0%     │
└─────────┴───────┴───────┴──────────────┘
```

### 结果文件格式

验证结果保存为JSON格式：

```json
[
  {
    "email": "<EMAIL>",
    "is_valid": true,
    "result": "valid",
    "carrier": "docomo",
    "domain": "docomo.ne.jp",
    "syntax_valid": true,
    "mx_valid": true,
    "smtp_valid": true,
    "error_message": null,
    "response_time": 1.23
  },
  {
    "email": "<EMAIL>",
    "is_valid": false,
    "result": "invalid",
    "carrier": null,
    "domain": "unknown.com",
    "syntax_valid": true,
    "mx_valid": false,
    "smtp_valid": false,
    "error_message": "No MX record found",
    "response_time": 0.45
  }
]
```

## 📈 步骤5: 结果分析和处理

### 筛选有效邮箱

```python
import json

# 读取验证结果
with open('results/validation_results.json', 'r') as f:
    results = json.load(f)

# 筛选有效邮箱
valid_emails = [r['email'] for r in results if r['is_valid']]

# 保存有效邮箱列表
with open('valid_emails.txt', 'w') as f:
    f.write('\n'.join(valid_emails))

print(f"有效邮箱数量: {len(valid_emails)}")
```

### 按运营商分类

```python
# 按运营商分组
carriers = {}
for result in results:
    carrier = result['carrier'] or 'unknown'
    if carrier not in carriers:
        carriers[carrier] = []
    carriers[carrier].append(result['email'])

# 保存分类结果
for carrier, emails in carriers.items():
    filename = f'{carrier}_emails.txt'
    with open(filename, 'w') as f:
        f.write('\n'.join(emails))
    print(f"{carrier}: {len(emails)} 个邮箱 -> {filename}")
```

## 🔧 常见问题和解决方案

### 问题1: 验证速度慢

**解决方案**：
```bash
# 增加并发数
python main.py batch emails.txt --use-proxy -w 30

# 检查代理状态
python main.py proxy-status

# 确保有足够的代理
```

### 问题2: 代理连接失败

**解决方案**：
```bash
# 检查代理配置
cat config/proxies.txt

# 测试代理连接
python main.py proxy-status

# 更新代理列表
```

### 问题3: 内存使用过高

**解决方案**：
```bash
# 分批处理大文件
python tools/email_list_manager.py

# 减少并发数
python main.py batch emails.txt --use-proxy -w 10
```

### 问题4: 验证结果不准确

**解决方案**：
- 使用高质量代理
- 降低验证频率
- 检查网络连接稳定性

## 📋 批量验证最佳实践

### 1. 文件准备
- ✅ 使用UTF-8编码
- ✅ 每行一个邮箱
- ✅ 去除重复邮箱
- ✅ 验证邮箱格式

### 2. 代理配置
- ✅ 使用多个高质量代理
- ✅ 定期检查代理状态
- ✅ 配置合适的轮换间隔

### 3. 性能优化
- ✅ 根据邮箱数量调整并发数
- ✅ 大文件分批处理
- ✅ 监控系统资源使用

### 4. 结果处理
- ✅ 及时保存验证结果
- ✅ 分析验证统计信息
- ✅ 按需筛选和分类

## 🎯 示例工作流程

### 完整的批量验证流程

```bash
# 1. 准备邮箱列表
echo "<EMAIL>
<EMAIL>
<EMAIL>" > my_emails.txt

# 2. 检查代理状态
python main.py proxy-status

# 3. 执行批量验证
python main.py batch my_emails.txt \
  --use-proxy \
  --output my_results.json \
  --workers 20

# 4. 查看结果
cat results/my_results.json

# 5. 筛选有效邮箱（可选）
python -c "
import json
with open('results/my_results.json', 'r') as f:
    results = json.load(f)
valid = [r['email'] for r in results if r['is_valid']]
with open('valid_emails.txt', 'w') as f:
    f.write('\n'.join(valid))
print(f'有效邮箱: {len(valid)} 个')
"
```

## 📞 获取帮助

如果在批量验证过程中遇到问题：

1. 查看日志文件：`logs/email_validator.log`
2. 检查代理状态：`python main.py proxy-status`
3. 参考使用示例：`USAGE_EXAMPLES.md`
4. 运行测试脚本：`python run_example.py`

---

🎉 **现在你已经掌握了批量验证的完整流程！开始验证你的邮箱列表吧！**
