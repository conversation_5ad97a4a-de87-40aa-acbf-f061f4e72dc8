# 使用示例和最佳实践

## 🎯 基础使用示例

### 1. 验证单个邮箱

#### 基本验证（不使用代理）
```bash
python main.<NAME_EMAIL>
```

输出示例：
```
Validating: <EMAIL>
✓ VALID
Carrier: docomo
Domain: docomo.ne.jp
Syntax: ✓
MX Record: ✓
SMTP: ✓
Response Time: 1.23s
```

#### 使用代理验证
```bash
python main.<NAME_EMAIL> --use-proxy
```

### 2. 批量验证邮箱

#### 准备邮箱列表文件
创建 `my_emails.txt` 文件：
```
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
```

#### 执行批量验证
```bash
python main.py batch my_emails.txt -o my_results.json --use-proxy -w 10
```

输出示例：
```
✓ Loaded 3/3 working proxies
Found 5 emails to validate
Validating emails: 100%|████████████| 5/5 [00:15<00:00,  3.12s/it]
✓ Results saved to: results/my_results.json

=== Validation Summary ===
Total emails: 5
Valid: 3
Invalid: 1
Errors: 1

=== Carrier Statistics ===
┌─────────┬───────┬───────┬──────────────┐
│ Carrier │ Total │ Valid │ Success Rate │
├─────────┼───────┼───────┼──────────────┤
│ DOCOMO  │   1   │   1   │    100.0%    │
│ AU_KDDI │   2   │   1   │     50.0%    │
│ SOFTBANK│   1   │   1   │    100.0%    │
│ UNKNOWN │   1   │   0   │      0.0%    │
└─────────┴───────┴───────┴──────────────┘
```

## 🔧 高级使用示例

### 3. 自定义配置验证

#### 使用自定义代理文件
```bash
python main.<NAME_EMAIL> --proxy-file custom_proxies.txt --use-proxy
```

#### 高并发批量验证
```bash
python main.py batch large_email_list.txt -w 50 --use-proxy -o high_speed_results.json
```

### 4. 代理管理

#### 检查代理状态
```bash
python main.py proxy-status
```

输出示例：
```
✓ Loaded 5/5 working proxies
=== Proxy Status ===
Total Proxies: 5
Working Proxies: 5
Failed Proxies: 0
Average Response Time: 1.45s

=== Proxy Details ===
┌─────────────────────────────────────┬────────┬──────────┬───────────────┐
│ Proxy                               │ Status │ Failures │ Response Time │
├─────────────────────────────────────┼────────┼──────────┼───────────────┤
│ global.rotgb.711proxy.com:10000     │   ✓    │    0     │     1.23s     │
│ global.rotgb.711proxy.com:10001     │   ✓    │    0     │     1.45s     │
│ global.rotgb.711proxy.com:10002     │   ✓    │    0     │     1.67s     │
└─────────────────────────────────────┴────────┴──────────┴───────────────┘
```

## 📊 结果分析示例

### 5. 验证结果格式

验证结果保存为JSON格式，包含详细信息：

```json
[
  {
    "email": "<EMAIL>",
    "is_valid": true,
    "result": "valid",
    "carrier": "docomo",
    "domain": "docomo.ne.jp",
    "syntax_valid": true,
    "mx_valid": true,
    "smtp_valid": true,
    "error_message": null,
    "response_time": 1.23
  },
  {
    "email": "<EMAIL>",
    "is_valid": false,
    "result": "invalid",
    "carrier": null,
    "domain": "unknown.com",
    "syntax_valid": true,
    "mx_valid": false,
    "smtp_valid": false,
    "error_message": "No MX record found",
    "response_time": 0.45
  }
]
```

### 6. 结果筛选和分析

#### 筛选有效邮箱
```python
import json

# 读取验证结果
with open('results/my_results.json', 'r') as f:
    results = json.load(f)

# 筛选有效邮箱
valid_emails = [r['email'] for r in results if r['is_valid']]
print(f"有效邮箱数量: {len(valid_emails)}")

# 按运营商分组
carriers = {}
for result in results:
    carrier = result['carrier'] or 'unknown'
    if carrier not in carriers:
        carriers[carrier] = []
    carriers[carrier].append(result['email'])

for carrier, emails in carriers.items():
    print(f"{carrier}: {len(emails)} 个邮箱")
```

## 🎯 特定场景示例

### 7. 日本运营商邮箱验证

#### DoCoMo邮箱验证
```bash
# 验证DoCoMo邮箱
python main.<NAME_EMAIL> --use-proxy
```

DoCoMo邮箱特点：
- 域名：docomo.ne.jp
- 最大长度：30字符
- 不允许连续特殊字符

#### au/KDDI邮箱验证
```bash
# 验证au邮箱
python main.<NAME_EMAIL> --use-proxy
python main.<NAME_EMAIL> --use-proxy
```

au/KDDI邮箱特点：
- 域名：au.com, ezweb.ne.jp, uqmobile.jp
- 禁止保留词
- 特殊字符限制

#### SoftBank邮箱验证
```bash
# 验证SoftBank邮箱
python main.<NAME_EMAIL> --use-proxy
python main.<NAME_EMAIL> --use-proxy
```

SoftBank邮箱特点：
- 域名：softbank.ne.jp, i.softbank.jp, vodafone.ne.jp
- 必须以字母开头
- 点号数量限制

### 8. 大规模验证示例

#### 处理10万个邮箱
```bash
# 创建大型邮箱列表
python -c "
import random
carriers = ['docomo.ne.jp', 'au.com', 'softbank.ne.jp', 'ezweb.ne.jp']
with open('large_list.txt', 'w') as f:
    for i in range(100000):
        domain = random.choice(carriers)
        f.write(f'user{i}@{domain}\n')
"

# 高并发验证
python main.py batch large_list.txt -w 100 --use-proxy -o large_results.json
```

## 🔍 故障排除示例

### 9. 常见问题处理

#### 代理连接失败
```bash
# 检查代理状态
python main.py proxy-status

# 如果代理失败，检查配置
cat config/proxies.txt

# 测试单个代理
python -c "
import requests
proxy = {'http': 'http://USER456549-zone-custom:<EMAIL>:10000'}
try:
    r = requests.get('http://httpbin.org/ip', proxies=proxy, timeout=10)
    print('代理工作正常:', r.json())
except Exception as e:
    print('代理连接失败:', e)
"
```

#### SMTP验证失败
```bash
# 检查网络连接
ping mail.docomo.ne.jp

# 测试SMTP连接
python -c "
import smtplib
try:
    smtp = smtplib.SMTP('mail.docomo.ne.jp', 25, timeout=10)
    smtp.helo()
    print('SMTP连接成功')
    smtp.quit()
except Exception as e:
    print('SMTP连接失败:', e)
"
```

### 10. 性能优化示例

#### 调整并发参数
```bash
# 低并发（稳定）
python main.py batch emails.txt -w 5 --use-proxy

# 中等并发（平衡）
python main.py batch emails.txt -w 20 --use-proxy

# 高并发（快速）
python main.py batch emails.txt -w 50 --use-proxy
```

#### 配置优化
编辑 `config/settings.json`：
```json
{
  "general": {
    "max_concurrent_checks": 100,
    "request_delay": 0.1,
    "retry_attempts": 2,
    "timeout": 15
  },
  "proxy": {
    "rotation_interval": 5,
    "health_check_interval": 180,
    "max_failures": 3
  }
}
```

## 📈 监控和日志示例

### 11. 日志分析

#### 查看验证日志
```bash
# 查看最新日志
tail -f logs/email_validator.log

# 搜索错误信息
grep "ERROR" logs/email_validator.log

# 统计验证结果
grep "validation" logs/email_validator.log | wc -l
```

#### 性能监控
```bash
# 监控验证速度
python -c "
import json
import time

start_time = time.time()
# 执行验证...
end_time = time.time()

with open('results/my_results.json', 'r') as f:
    results = json.load(f)

total_emails = len(results)
total_time = end_time - start_time
speed = total_emails / total_time

print(f'验证速度: {speed:.2f} 邮箱/秒')
print(f'总耗时: {total_time:.2f} 秒')
"
```

## 🎯 最佳实践

### 12. 推荐配置

#### 生产环境配置
- 并发数：20-50
- 代理轮换间隔：10-30秒
- 重试次数：2-3次
- 超时时间：15-30秒

#### 测试环境配置
- 并发数：5-10
- 详细日志：开启
- 代理健康检查：频繁

#### 大规模验证配置
- 并发数：50-100
- 代理池：10+个代理
- 分批处理：每批1000-5000个邮箱

这些示例涵盖了从基础使用到高级配置的各种场景，帮助你充分利用这个邮件验证工具。
