#!/usr/bin/env python3
"""
批量邮箱验证脚本
"""

import sys
import os
import re
import json
import time
import socket
from pathlib import Path

class SimpleEmailValidator:
    def __init__(self):
        self.email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        self.japan_domains = {
            'docomo.ne.jp': 'DoCoMo',
            'au.com': 'au/KDDI',
            'ezweb.ne.jp': 'au/KDDI',
            'uqmobile.jp': 'au/KDDI',
            'softbank.ne.jp': 'SoftBank',
            'i.softbank.jp': 'SoftBank',
            'vodafone.ne.jp': 'SoftBank',
            'disney.ne.jp': 'SoftBank'
        }
    
    def validate_syntax(self, email):
        """验证邮箱语法"""
        return bool(self.email_pattern.match(email.strip()))
    
    def detect_carrier(self, email):
        """检测日本运营商"""
        domain = email.split('@')[-1].lower()
        return self.japan_domains.get(domain, 'Unknown')
    
    def check_domain(self, domain):
        """检查域名是否可解析"""
        try:
            socket.gethostbyname(domain)
            return True
        except socket.gaierror:
            return False
    
    def validate_email(self, email):
        """验证单个邮箱"""
        start_time = time.time()
        
        result = {
            'email': email,
            'is_valid': False,
            'syntax_valid': False,
            'domain_valid': False,
            'carrier': 'Unknown',
            'error_message': None,
            'response_time': 0
        }
        
        try:
            # 语法验证
            result['syntax_valid'] = self.validate_syntax(email)
            if not result['syntax_valid']:
                result['error_message'] = 'Invalid email syntax'
                return result
            
            # 检测运营商
            result['carrier'] = self.detect_carrier(email)
            
            # 域名检查
            domain = email.split('@')[-1]
            result['domain_valid'] = self.check_domain(domain)
            
            if not result['domain_valid']:
                result['error_message'] = 'Domain not reachable'
                return result
            
            # 如果语法和域名都有效，标记为有效
            result['is_valid'] = True
            
        except Exception as e:
            result['error_message'] = str(e)
        
        finally:
            result['response_time'] = time.time() - start_time
        
        return result

def batch_validate(email_file):
    """批量验证邮箱"""
    # 读取邮箱列表
    try:
        with open(email_file, 'r', encoding='utf-8') as f:
            emails = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {email_file}")
        return
    
    print(f"📧 开始验证 {len(emails)} 个邮箱地址...")
    print("=" * 60)
    
    validator = SimpleEmailValidator()
    results = []
    valid_count = 0
    
    for i, email in enumerate(emails, 1):
        print(f"验证 {i:3d}/{len(emails)}: {email:<35}", end=" ")
        
        result = validator.validate_email(email)
        results.append(result)
        
        if result['is_valid']:
            print("✅ 有效")
            valid_count += 1
        else:
            print(f"❌ 无效 - {result['error_message']}")
    
    # 显示统计
    print("\n" + "=" * 60)
    print("📊 验证结果统计:")
    print(f"总邮箱数: {len(emails)}")
    print(f"有效邮箱: {valid_count}")
    print(f"无效邮箱: {len(emails) - valid_count}")
    print(f"成功率: {(valid_count/len(emails)*100):.1f}%")
    
    # 按运营商统计
    carrier_stats = {}
    for result in results:
        carrier = result['carrier']
        if carrier not in carrier_stats:
            carrier_stats[carrier] = {'total': 0, 'valid': 0}
        carrier_stats[carrier]['total'] += 1
        if result['is_valid']:
            carrier_stats[carrier]['valid'] += 1
    
    print("\n📱 运营商统计:")
    for carrier, stats in carrier_stats.items():
        rate = (stats['valid'] / stats['total'] * 100) if stats['total'] > 0 else 0
        print(f"{carrier:<10}: {stats['valid']:3d}/{stats['total']:3d} ({rate:5.1f}%)")
    
    # 保存结果
    Path("results").mkdir(exist_ok=True)
    output_file = f"results/batch_results_{int(time.time())}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 结果已保存到: {output_file}")
    
    # 保存有效邮箱
    valid_emails = [r['email'] for r in results if r['is_valid']]
    if valid_emails:
        valid_file = "results/valid_emails.txt"
        with open(valid_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(valid_emails))
        print(f"✅ 有效邮箱列表: {valid_file}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("🎯 批量邮箱验证工具")
        print("使用方法: python batch_validator.py <邮箱文件>")
        print("示例: python batch_validator.py data/sample_emails.txt")
        sys.exit(1)
    
    email_file = sys.argv[1]
    if not os.path.exists(email_file):
        print(f"❌ 文件不存在: {email_file}")
        sys.exit(1)
    
    batch_validate(email_file)
