
# 📥 日本邮箱验证工具 - 下载和使用指南

## 🎯 工具简介

这是一个专门针对日本三大运营商（DoCoMo、au/KDDI、SoftBank）的高级邮箱验证工具，支持：
- 批量验证邮箱地址
- 代理轮换支持
- 运营商特定验证规则
- 详细验证报告

## 📦 下载文件

下载最新的项目包：`japan-email-validator-fixed-v20250923_2222.zip`

## 🚀 使用步骤

### 第一步：解压文件
将下载的ZIP文件解压到你的工作目录。

### 第二步：安装依赖
```bash
python quick_install.py
```

### 第三步：配置代理
编辑 `config/proxies.txt`，添加你的代理信息：
```
global.rotgb.711proxy.com:10000:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10001:USER456549-zone-custom:ec5512
```

### 第四步：准备邮箱列表
创建一个文本文件，每行一个邮箱：
```
<EMAIL>
<EMAIL>
<EMAIL>
```

### 第五步：执行验证
```bash
python batch_validator.py your_emails.txt
```

## 📊 验证结果示例

```
📧 开始验证 5 个邮箱地址...
验证   1/5: <EMAIL>     ✅ 有效
验证   2/5: <EMAIL>           ✅ 有效
验证   3/5: <EMAIL> ❌ 无效

📊 验证结果统计:
总邮箱数: 5
有效邮箱: 3
成功率: 60.0%

📱 运营商统计:
DoCoMo    :   1/  1 (100.0%)
au/KDDI   :   2/  2 (100.0%)
SoftBank  :   0/  2 (  0.0%)
```

## 🎯 支持的邮箱类型

- **DoCoMo**: docomo.ne.jp
- **au/KDDI**: au.com, ezweb.ne.jp, uqmobile.jp  
- **SoftBank**: softbank.ne.jp, i.softbank.jp, vodafone.ne.jp, disney.ne.jp

## 📁 项目结构

```
japan-email-validator/
├── batch_validator.py     # 主验证脚本
├── quick_install.py       # 安装脚本
├── config/
│   └── proxies.txt        # 代理配置
├── data/
│   └── sample_emails.txt  # 示例邮箱
├── results/               # 验证结果
└── 使用说明.txt           # 详细说明
```

## 🔧 常见问题

**Q: 如何添加更多代理？**
A: 编辑 `config/proxies.txt`，每行添加一个代理，格式：`host:port:username:password`

**Q: 支持多大的邮箱列表？**
A: 理论上无限制，建议单次验证不超过10000个邮箱

**Q: 验证速度如何？**
A: 取决于网络和代理质量，通常每秒可验证5-20个邮箱

**Q: 结果保存在哪里？**
A: `results/` 目录下，包含JSON详细结果和TXT有效邮箱列表

## 📞 技术支持

如果遇到问题：
1. 确保Python版本3.6+
2. 检查网络连接
3. 验证代理配置
4. 确认文件格式正确

---
🎉 **开始验证你的日本邮箱列表吧！**
