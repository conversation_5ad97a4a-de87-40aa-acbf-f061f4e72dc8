#!/usr/bin/env python3
"""
快速安装和修复脚本
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def install_dependencies():
    """安装必需的依赖包"""
    print("🔧 安装Python依赖包...")
    
    packages = ["requests", "colorama", "tqdm", "click", "tabulate"]
    
    for package in packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {package} 安装失败")
    
    return True

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建项目目录...")
    
    directories = ["config", "logs", "results", "data"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ 创建目录: {directory}")

def create_config_files():
    """创建配置文件"""
    print("\n⚙️ 创建配置文件...")
    
    # 代理配置文件
    proxy_config = """# 代理配置文件
# 格式: host:port:username:password
# 请将下面的示例替换为你的实际代理信息
global.rotgb.711proxy.com:10000:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10001:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10002:USER456549-zone-custom:ec5512
"""
    
    with open("config/proxies.txt", "w", encoding="utf-8") as f:
        f.write(proxy_config)
    print("✅ 创建代理配置文件")

def create_sample_emails():
    """创建示例邮箱文件"""
    print("\n📧 创建示例邮箱文件...")
    
    sample_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    with open("data/sample_emails.txt", "w", encoding="utf-8") as f:
        f.write('\n'.join(sample_emails))
    
    print("✅ 创建示例邮箱文件: data/sample_emails.txt")

def create_batch_validator():
    """创建批量验证脚本"""
    print("\n📝 创建批量验证脚本...")
    
    script_content = '''#!/usr/bin/env python3
"""
批量邮箱验证脚本
"""

import sys
import os
import re
import json
import time
import socket
from pathlib import Path

class SimpleEmailValidator:
    def __init__(self):
        self.email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$')
        self.japan_domains = {
            'docomo.ne.jp': 'DoCoMo',
            'au.com': 'au/KDDI',
            'ezweb.ne.jp': 'au/KDDI',
            'uqmobile.jp': 'au/KDDI',
            'softbank.ne.jp': 'SoftBank',
            'i.softbank.jp': 'SoftBank',
            'vodafone.ne.jp': 'SoftBank',
            'disney.ne.jp': 'SoftBank'
        }
    
    def validate_syntax(self, email):
        """验证邮箱语法"""
        return bool(self.email_pattern.match(email.strip()))
    
    def detect_carrier(self, email):
        """检测日本运营商"""
        domain = email.split('@')[-1].lower()
        return self.japan_domains.get(domain, 'Unknown')
    
    def check_domain(self, domain):
        """检查域名是否可解析"""
        try:
            socket.gethostbyname(domain)
            return True
        except socket.gaierror:
            return False
    
    def validate_email(self, email):
        """验证单个邮箱"""
        start_time = time.time()
        
        result = {
            'email': email,
            'is_valid': False,
            'syntax_valid': False,
            'domain_valid': False,
            'carrier': 'Unknown',
            'error_message': None,
            'response_time': 0
        }
        
        try:
            # 语法验证
            result['syntax_valid'] = self.validate_syntax(email)
            if not result['syntax_valid']:
                result['error_message'] = 'Invalid email syntax'
                return result
            
            # 检测运营商
            result['carrier'] = self.detect_carrier(email)
            
            # 域名检查
            domain = email.split('@')[-1]
            result['domain_valid'] = self.check_domain(domain)
            
            if not result['domain_valid']:
                result['error_message'] = 'Domain not reachable'
                return result
            
            # 如果语法和域名都有效，标记为有效
            result['is_valid'] = True
            
        except Exception as e:
            result['error_message'] = str(e)
        
        finally:
            result['response_time'] = time.time() - start_time
        
        return result

def batch_validate(email_file):
    """批量验证邮箱"""
    # 读取邮箱列表
    try:
        with open(email_file, 'r', encoding='utf-8') as f:
            emails = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {email_file}")
        return
    
    print(f"📧 开始验证 {len(emails)} 个邮箱地址...")
    print("=" * 60)
    
    validator = SimpleEmailValidator()
    results = []
    valid_count = 0
    
    for i, email in enumerate(emails, 1):
        print(f"验证 {i:3d}/{len(emails)}: {email:<35}", end=" ")
        
        result = validator.validate_email(email)
        results.append(result)
        
        if result['is_valid']:
            print("✅ 有效")
            valid_count += 1
        else:
            print(f"❌ 无效 - {result['error_message']}")
    
    # 显示统计
    print("\\n" + "=" * 60)
    print("📊 验证结果统计:")
    print(f"总邮箱数: {len(emails)}")
    print(f"有效邮箱: {valid_count}")
    print(f"无效邮箱: {len(emails) - valid_count}")
    print(f"成功率: {(valid_count/len(emails)*100):.1f}%")
    
    # 按运营商统计
    carrier_stats = {}
    for result in results:
        carrier = result['carrier']
        if carrier not in carrier_stats:
            carrier_stats[carrier] = {'total': 0, 'valid': 0}
        carrier_stats[carrier]['total'] += 1
        if result['is_valid']:
            carrier_stats[carrier]['valid'] += 1
    
    print("\\n📱 运营商统计:")
    for carrier, stats in carrier_stats.items():
        rate = (stats['valid'] / stats['total'] * 100) if stats['total'] > 0 else 0
        print(f"{carrier:<10}: {stats['valid']:3d}/{stats['total']:3d} ({rate:5.1f}%)")
    
    # 保存结果
    Path("results").mkdir(exist_ok=True)
    output_file = f"results/batch_results_{int(time.time())}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\\n💾 结果已保存到: {output_file}")
    
    # 保存有效邮箱
    valid_emails = [r['email'] for r in results if r['is_valid']]
    if valid_emails:
        valid_file = "results/valid_emails.txt"
        with open(valid_file, 'w', encoding='utf-8') as f:
            f.write('\\n'.join(valid_emails))
        print(f"✅ 有效邮箱列表: {valid_file}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("🎯 批量邮箱验证工具")
        print("使用方法: python batch_validator.py <邮箱文件>")
        print("示例: python batch_validator.py data/sample_emails.txt")
        sys.exit(1)
    
    email_file = sys.argv[1]
    if not os.path.exists(email_file):
        print(f"❌ 文件不存在: {email_file}")
        sys.exit(1)
    
    batch_validate(email_file)
'''
    
    with open("batch_validator.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("✅ 创建批量验证脚本: batch_validator.py")

def main():
    """主安装函数"""
    print("🎯 高级邮箱验证工具 - 快速安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 错误: 需要Python 3.6或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 安装依赖
    install_dependencies()
    
    # 创建目录
    create_directories()
    
    # 创建配置文件
    create_config_files()
    
    # 创建示例文件
    create_sample_emails()
    
    # 创建批量验证脚本
    create_batch_validator()
    
    print("\n🎉 安装完成!")
    print("\n📋 使用说明:")
    print("1. 编辑 config/proxies.txt 添加你的代理信息")
    print("2. 准备你的邮箱列表文件 (每行一个邮箱)")
    print("3. 运行批量验证:")
    print("   python batch_validator.py data/sample_emails.txt")
    print("   python batch_validator.py your_emails.txt")
    
    print("\n📁 项目文件:")
    print("- batch_validator.py (批量验证脚本)")
    print("- config/proxies.txt (代理配置)")
    print("- data/sample_emails.txt (示例邮箱)")
    print("- results/ (验证结果目录)")

if __name__ == "__main__":
    main()
