import re
import json
import socket
import smtplib
import dns.resolver
from email.mime.text import MIMEText
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass
from enum import Enum

class ValidationResult(Enum):
    VALID = "valid"
    INVALID = "invalid"
    UNKNOWN = "unknown"
    ERROR = "error"

@dataclass
class EmailValidationResult:
    email: str
    is_valid: bool
    result: ValidationResult
    carrier: Optional[str] = None
    domain: Optional[str] = None
    syntax_valid: bool = False
    mx_valid: bool = False
    smtp_valid: bool = False
    error_message: Optional[str] = None
    response_time: float = 0.0

class EmailValidator:
    def __init__(self, config_path: str = "config/email_domains.json"):
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config(config_path)
        self.japan_carriers = self.config.get("japan_carriers", {})
        
    def _load_config(self, config_path: str) -> Dict:
        """加载邮箱域名配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load config: {e}")
            return {}
    
    def validate_syntax(self, email: str) -> bool:
        """验证邮箱语法"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    def detect_carrier(self, email: str) -> Optional[str]:
        """检测日本运营商"""
        domain = email.split('@')[-1].lower()
        
        for carrier, info in self.japan_carriers.items():
            if domain in info.get("domains", []):
                return carrier
        return None
    
    def validate_carrier_specific(self, email: str, carrier: str) -> Tuple[bool, str]:
        """运营商特定验证"""
        if carrier not in self.japan_carriers:
            return False, "Unknown carrier"
        
        carrier_info = self.japan_carriers[carrier]
        rules = carrier_info.get("validation_rules", {})
        
        local_part = email.split('@')[0]
        
        # 长度检查
        min_len = rules.get("min_length", 1)
        max_len = rules.get("max_length", 64)
        if not (min_len <= len(local_part) <= max_len):
            return False, f"Length must be between {min_len} and {max_len}"
        
        # 字符检查
        allowed_chars = rules.get("allowed_chars", "")
        if allowed_chars and not all(c in allowed_chars for c in local_part):
            return False, "Contains invalid characters"
        
        # 特殊规则检查
        special_checks = rules.get("special_checks", [])
        if "no_consecutive_dots" in special_checks and ".." in local_part:
            return False, "Consecutive dots not allowed"
        
        if "no_start_end_special" in special_checks:
            if local_part.startswith(('.', '_', '-')) or local_part.endswith(('.', '_', '-')):
                return False, "Cannot start or end with special characters"
        
        return True, "Valid"
    
    def check_mx_record(self, domain: str) -> bool:
        """检查MX记录"""
        try:
            mx_records = dns.resolver.resolve(domain, 'MX')
            return len(mx_records) > 0
        except Exception as e:
            self.logger.debug(f"MX check failed for {domain}: {e}")
            return False
    
    def check_smtp_connection(self, email: str, proxy_manager=None) -> Tuple[bool, str]:
        """SMTP连接检查"""
        domain = email.split('@')[-1]
        carrier = self.detect_carrier(email)
        
        # 获取SMTP服务器
        smtp_servers = []
        if carrier and carrier in self.japan_carriers:
            smtp_servers = self.japan_carriers[carrier].get("smtp_servers", [])
        
        # 添加通用SMTP服务器
        smtp_servers.extend([f"mail.{domain}", f"smtp.{domain}"])
        
        ports = self.config.get("common_smtp_ports", [25, 587, 465])
        timeout = self.config.get("timeout_settings", {}).get("smtp_timeout", 15)
        
        for server in smtp_servers:
            for port in ports:
                try:
                    # 如果有代理，这里需要通过代理连接
                    if proxy_manager:
                        # 代理SMTP连接逻辑
                        pass
                    else:
                        with smtplib.SMTP(server, port, timeout=timeout) as smtp:
                            smtp.helo()
                            code, message = smtp.rcpt(email)
                            if code == 250:
                                return True, "SMTP validation successful"
                            elif code in [550, 551, 553]:
                                return False, f"SMTP rejected: {message}"
                except Exception as e:
                    self.logger.debug(f"SMTP check failed for {server}:{port} - {e}")
                    continue
        
        return False, "SMTP validation failed"

    def validate_email(self, email: str, proxy_manager=None) -> EmailValidationResult:
        """完整的邮箱验证"""
        import time
        start_time = time.time()

        result = EmailValidationResult(
            email=email,
            is_valid=False,
            result=ValidationResult.INVALID
        )

        try:
            # 语法验证
            result.syntax_valid = self.validate_syntax(email)
            if not result.syntax_valid:
                result.error_message = "Invalid email syntax"
                return result

            # 提取域名和检测运营商
            result.domain = email.split('@')[-1]
            result.carrier = self.detect_carrier(email)

            # 运营商特定验证
            if result.carrier:
                carrier_valid, carrier_msg = self.validate_carrier_specific(email, result.carrier)
                if not carrier_valid:
                    result.error_message = f"Carrier validation failed: {carrier_msg}"
                    return result

            # MX记录检查
            result.mx_valid = self.check_mx_record(result.domain)
            if not result.mx_valid:
                result.error_message = "No MX record found"
                result.result = ValidationResult.UNKNOWN
                return result

            # SMTP验证
            result.smtp_valid, smtp_message = self.check_smtp_connection(email, proxy_manager)

            # 最终结果判断
            if result.syntax_valid and result.mx_valid and result.smtp_valid:
                result.is_valid = True
                result.result = ValidationResult.VALID
            elif result.syntax_valid and result.mx_valid:
                result.result = ValidationResult.UNKNOWN
                result.error_message = smtp_message
            else:
                result.result = ValidationResult.INVALID
                result.error_message = smtp_message or "Validation failed"

        except Exception as e:
            result.result = ValidationResult.ERROR
            result.error_message = f"Validation error: {str(e)}"
            self.logger.error(f"Email validation error for {email}: {e}")

        finally:
            result.response_time = time.time() - start_time

        return result
