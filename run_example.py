#!/usr/bin/env python3
"""
邮件验证工具使用示例脚本
演示如何使用API进行邮件验证
"""

import os
import sys
import json
import time
from pathlib import Path

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from email_validator import JapanEmailValidator
from proxy_manager import ProxyManager
from japan_carriers import JapanCarrierValidator

def example_single_validation():
    """单个邮箱验证示例"""
    print("=== 单个邮箱验证示例 ===")
    
    # 初始化验证器
    validator = JapanEmailValidator()
    carrier_validator = JapanCarrierValidator()
    
    # 测试邮箱
    test_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    for email in test_emails:
        print(f"\n验证邮箱: {email}")
        
        # 运营商检测
        carrier_result = carrier_validator.validate_carrier_format(email)
        print(f"运营商: {carrier_result.carrier.value}")
        print(f"运营商验证: {'✓' if carrier_result.is_valid else '✗'}")
        
        # 完整验证
        result = validator.validate_email(email)
        print(f"最终结果: {'✓ 有效' if result.is_valid else '✗ 无效'}")
        print(f"响应时间: {result.response_time:.2f}s")
        
        if result.error_message:
            print(f"错误信息: {result.error_message}")

def example_proxy_usage():
    """代理使用示例"""
    print("\n=== 代理使用示例 ===")
    
    proxy_file = "config/proxies.txt"
    if not os.path.exists(proxy_file):
        print(f"代理文件不存在: {proxy_file}")
        return
    
    # 初始化代理管理器
    proxy_manager = ProxyManager(proxy_file)
    
    # 显示代理统计
    stats = proxy_manager.get_proxy_stats()
    print(f"总代理数: {stats['total_proxies']}")
    print(f"可用代理: {stats['working_proxies']}")
    print(f"平均响应时间: {stats['average_response_time']:.2f}s")
    
    # 测试代理
    proxy = proxy_manager.get_next_proxy()
    if proxy:
        print(f"\n测试代理: {proxy.host}:{proxy.port}")
        is_working, response_time = proxy_manager.test_proxy(proxy)
        print(f"代理状态: {'✓ 可用' if is_working else '✗ 不可用'}")
        print(f"响应时间: {response_time:.2f}s")

def example_batch_validation():
    """批量验证示例"""
    print("\n=== 批量验证示例 ===")
    
    # 创建测试邮箱列表
    test_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    # 保存到文件
    test_file = "data/test_emails.txt"
    Path("data").mkdir(exist_ok=True)
    
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(test_emails))
    
    print(f"创建测试文件: {test_file}")
    print(f"包含 {len(test_emails)} 个邮箱")
    
    # 初始化验证器
    validator = JapanEmailValidator()
    
    # 批量验证
    print("\n开始批量验证...")
    start_time = time.time()
    
    results = []
    for email in test_emails:
        result = validator.validate_email(email)
        results.append(result)
        print(f"验证 {email}: {'✓' if result.is_valid else '✗'}")
    
    end_time = time.time()
    
    # 统计结果
    total = len(results)
    valid = sum(1 for r in results if r.is_valid)
    invalid = total - valid
    
    print(f"\n=== 验证结果统计 ===")
    print(f"总数: {total}")
    print(f"有效: {valid}")
    print(f"无效: {invalid}")
    print(f"成功率: {(valid/total)*100:.1f}%")
    print(f"总耗时: {end_time - start_time:.2f}s")
    print(f"平均速度: {total/(end_time - start_time):.2f} 邮箱/秒")
    
    # 保存结果
    results_data = []
    for result in results:
        results_data.append({
            'email': result.email,
            'is_valid': result.is_valid,
            'carrier': result.carrier,
            'domain': result.domain,
            'response_time': result.response_time,
            'error_message': result.error_message
        })
    
    results_file = "results/example_results.json"
    Path("results").mkdir(exist_ok=True)
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results_data, f, indent=2, ensure_ascii=False)
    
    print(f"结果已保存到: {results_file}")

def example_carrier_specific():
    """运营商特定验证示例"""
    print("\n=== 运营商特定验证示例 ===")
    
    carrier_validator = JapanCarrierValidator()
    
    # 测试不同运营商的邮箱
    test_cases = [
        ("<EMAIL>", "DoCoMo"),
        ("<EMAIL>", "au/KDDI"),
        ("<EMAIL>", "SoftBank"),
        ("<EMAIL>", "au/KDDI"),
        ("<EMAIL>", "SoftBank"),
        ("<EMAIL>", "未知")
    ]
    
    for email, expected_carrier in test_cases:
        print(f"\n测试邮箱: {email}")
        print(f"预期运营商: {expected_carrier}")
        
        # 检测运营商
        carrier = carrier_validator.detect_carrier(email)
        print(f"检测结果: {carrier.value}")
        
        # 运营商特定验证
        result = carrier_validator.validate_carrier_format(email)
        print(f"格式验证: {'✓' if result.is_valid else '✗'}")
        print(f"域名验证: {'✓' if result.domain_valid else '✗'}")
        print(f"格式验证: {'✓' if result.format_valid else '✗'}")
        print(f"特定规则: {'✓' if result.carrier_specific_valid else '✗'}")
        
        if result.error_message:
            print(f"错误信息: {result.error_message}")

def example_configuration():
    """配置示例"""
    print("\n=== 配置示例 ===")
    
    # 检查配置文件
    config_files = [
        "config/proxies.txt",
        "config/email_domains.json", 
        "config/settings.json"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✓ 配置文件存在: {config_file}")
            
            # 显示文件大小
            size = os.path.getsize(config_file)
            print(f"  文件大小: {size} 字节")
            
            # 如果是JSON文件，验证格式
            if config_file.endswith('.json'):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        json.load(f)
                    print(f"  JSON格式: ✓ 有效")
                except json.JSONDecodeError as e:
                    print(f"  JSON格式: ✗ 无效 - {e}")
        else:
            print(f"✗ 配置文件缺失: {config_file}")

def main():
    """主函数"""
    print("邮件验证工具 - 使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        example_configuration()
        example_single_validation()
        example_carrier_specific()
        example_proxy_usage()
        example_batch_validation()
        
        print("\n" + "=" * 50)
        print("所有示例运行完成！")
        print("\n下一步:")
        print("1. 配置你的代理文件 config/proxies.txt")
        print("2. 准备要验证的邮箱列表")
        print("3. 使用命令行工具进行验证:")
        print("   python main.<NAME_EMAIL> --use-proxy")
        print("   python main.py batch your_emails.txt --use-proxy")
        
    except Exception as e:
        print(f"\n运行示例时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
