#!/usr/bin/env python3
"""
高级邮件验证工具 - 专门针对日本三大运营商
支持代理、批量处理、详细报告
"""

import os
import sys
import json
import time
import logging
from pathlib import Path
from typing import List, Dict
import click
from colorama import init, Fore, Style
from tqdm import tqdm
from tabulate import tabulate
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from email_validator import EmailValidator, EmailValidationResult
from proxy_manager import ProxyManager
from japan_carriers import JapanCarrierValidator, JapanCarrier

# 初始化colorama
init()

class EmailValidatorCLI:
    def __init__(self):
        self.setup_logging()
        self.email_validator = EmailValidator()
        self.proxy_manager = None
        self.carrier_validator = JapanCarrierValidator()
        self.results = []
        self.lock = threading.Lock()
        
    def setup_logging(self):
        """设置日志"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "email_validator.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def init_proxy_manager(self, proxy_file: str = "config/proxies.txt"):
        """初始化代理管理器"""
        if os.path.exists(proxy_file):
            self.proxy_manager = ProxyManager(proxy_file)
            stats = self.proxy_manager.get_proxy_stats()
            click.echo(f"{Fore.GREEN}✓ Loaded {stats['working_proxies']}/{stats['total_proxies']} working proxies{Style.RESET_ALL}")
        else:
            click.echo(f"{Fore.YELLOW}⚠ Proxy file not found: {proxy_file}{Style.RESET_ALL}")
    
    def validate_single_email(self, email: str) -> EmailValidationResult:
        """验证单个邮箱"""
        # 首先进行运营商特定验证
        carrier_result = self.carrier_validator.validate_carrier_format(email)
        
        # 然后进行完整验证
        result = self.email_validator.validate_email(email, self.proxy_manager)
        
        # 合并运营商信息
        if carrier_result.carrier != JapanCarrier.UNKNOWN:
            result.carrier = carrier_result.carrier.value
        
        return result
    
    def validate_batch(self, emails: List[str], max_workers: int = 10) -> List[EmailValidationResult]:
        """批量验证邮箱"""
        results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_email = {
                executor.submit(self.validate_single_email, email): email 
                for email in emails
            }
            
            # 使用进度条显示进度
            with tqdm(total=len(emails), desc="Validating emails") as pbar:
                for future in as_completed(future_to_email):
                    email = future_to_email[future]
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        self.logger.error(f"Error validating {email}: {e}")
                        # 创建错误结果
                        error_result = EmailValidationResult(
                            email=email,
                            is_valid=False,
                            result="error",
                            error_message=str(e)
                        )
                        results.append(error_result)
                    finally:
                        pbar.update(1)
        
        return results
    
    def save_results(self, results: List[EmailValidationResult], output_file: str):
        """保存结果到文件"""
        output_dir = Path("results")
        output_dir.mkdir(exist_ok=True)
        
        output_path = output_dir / output_file
        
        # 转换为字典格式
        results_data = []
        for result in results:
            results_data.append({
                'email': result.email,
                'is_valid': result.is_valid,
                'result': result.result.value if hasattr(result.result, 'value') else str(result.result),
                'carrier': result.carrier,
                'domain': result.domain,
                'syntax_valid': result.syntax_valid,
                'mx_valid': result.mx_valid,
                'smtp_valid': result.smtp_valid,
                'error_message': result.error_message,
                'response_time': result.response_time
            })
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, indent=2, ensure_ascii=False)
        
        click.echo(f"{Fore.GREEN}✓ Results saved to: {output_path}{Style.RESET_ALL}")
    
    def print_summary(self, results: List[EmailValidationResult]):
        """打印验证摘要"""
        total = len(results)
        valid = sum(1 for r in results if r.is_valid)
        invalid = sum(1 for r in results if not r.is_valid and r.result.value != 'error')
        errors = sum(1 for r in results if r.result.value == 'error')
        
        # 按运营商统计
        carrier_stats = {}
        for result in results:
            carrier = result.carrier or 'unknown'
            if carrier not in carrier_stats:
                carrier_stats[carrier] = {'total': 0, 'valid': 0}
            carrier_stats[carrier]['total'] += 1
            if result.is_valid:
                carrier_stats[carrier]['valid'] += 1
        
        # 打印总体统计
        click.echo(f"\n{Fore.CYAN}=== Validation Summary ==={Style.RESET_ALL}")
        click.echo(f"Total emails: {total}")
        click.echo(f"{Fore.GREEN}Valid: {valid}{Style.RESET_ALL}")
        click.echo(f"{Fore.RED}Invalid: {invalid}{Style.RESET_ALL}")
        click.echo(f"{Fore.YELLOW}Errors: {errors}{Style.RESET_ALL}")
        
        # 打印运营商统计
        if carrier_stats:
            click.echo(f"\n{Fore.CYAN}=== Carrier Statistics ==={Style.RESET_ALL}")
            table_data = []
            for carrier, stats in carrier_stats.items():
                valid_rate = (stats['valid'] / stats['total']) * 100 if stats['total'] > 0 else 0
                table_data.append([
                    carrier.upper(),
                    stats['total'],
                    stats['valid'],
                    f"{valid_rate:.1f}%"
                ])
            
            click.echo(tabulate(
                table_data,
                headers=['Carrier', 'Total', 'Valid', 'Success Rate'],
                tablefmt='grid'
            ))

@click.group()
def cli():
    """高级邮件验证工具 - 专门针对日本三大运营商"""
    pass

@cli.command()
@click.argument('email')
@click.option('--use-proxy', is_flag=True, help='使用代理进行验证')
@click.option('--proxy-file', default='config/proxies.txt', help='代理文件路径')
def validate(email, use_proxy, proxy_file):
    """验证单个邮箱地址"""
    validator = EmailValidatorCLI()
    
    if use_proxy:
        validator.init_proxy_manager(proxy_file)
    
    click.echo(f"Validating: {email}")
    
    result = validator.validate_single_email(email)
    
    # 显示结果
    if result.is_valid:
        click.echo(f"{Fore.GREEN}✓ VALID{Style.RESET_ALL}")
    else:
        click.echo(f"{Fore.RED}✗ INVALID{Style.RESET_ALL}")
    
    # 显示详细信息
    click.echo(f"Carrier: {result.carrier or 'Unknown'}")
    click.echo(f"Domain: {result.domain}")
    click.echo(f"Syntax: {'✓' if result.syntax_valid else '✗'}")
    click.echo(f"MX Record: {'✓' if result.mx_valid else '✗'}")
    click.echo(f"SMTP: {'✓' if result.smtp_valid else '✗'}")
    click.echo(f"Response Time: {result.response_time:.2f}s")
    
    if result.error_message:
        click.echo(f"Error: {result.error_message}")

@cli.command()
@click.argument('input_file')
@click.option('--output', '-o', default='validation_results.json', help='输出文件名')
@click.option('--workers', '-w', default=10, help='并发工作线程数')
@click.option('--use-proxy', is_flag=True, help='使用代理进行验证')
@click.option('--proxy-file', default='config/proxies.txt', help='代理文件路径')
def batch(input_file, output, workers, use_proxy, proxy_file):
    """批量验证邮箱地址"""
    validator = EmailValidatorCLI()
    
    if use_proxy:
        validator.init_proxy_manager(proxy_file)
    
    # 读取邮箱列表
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            emails = [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        click.echo(f"{Fore.RED}Error: File not found: {input_file}{Style.RESET_ALL}")
        return
    
    click.echo(f"Found {len(emails)} emails to validate")
    
    # 批量验证
    results = validator.validate_batch(emails, workers)
    
    # 保存结果
    validator.save_results(results, output)
    
    # 显示摘要
    validator.print_summary(results)

@cli.command()
@click.option('--proxy-file', default='config/proxies.txt', help='代理文件路径')
def proxy_status(proxy_file):
    """检查代理状态"""
    validator = EmailValidatorCLI()
    validator.init_proxy_manager(proxy_file)

    if not validator.proxy_manager:
        click.echo(f"{Fore.RED}No proxy manager initialized{Style.RESET_ALL}")
        return

    stats = validator.proxy_manager.get_proxy_stats()

    click.echo(f"{Fore.CYAN}=== Proxy Status ==={Style.RESET_ALL}")
    click.echo(f"Total Proxies: {stats['total_proxies']}")
    click.echo(f"Working Proxies: {stats['working_proxies']}")
    click.echo(f"Failed Proxies: {stats['failed_proxies']}")
    click.echo(f"Average Response Time: {stats['average_response_time']:.2f}s")

    # 显示详细代理信息
    table_data = []
    for proxy in validator.proxy_manager.proxies:
        status = "✓" if proxy.is_working else "✗"
        table_data.append([
            f"{proxy.host}:{proxy.port}",
            status,
            proxy.failures,
            f"{proxy.response_time:.2f}s" if proxy.response_time > 0 else "N/A"
        ])

    if table_data:
        click.echo(f"\n{Fore.CYAN}=== Proxy Details ==={Style.RESET_ALL}")
        click.echo(tabulate(
            table_data,
            headers=['Proxy', 'Status', 'Failures', 'Response Time'],
            tablefmt='grid'
        ))

@cli.command()
def setup():
    """初始化项目配置"""
    click.echo(f"{Fore.CYAN}Setting up Email Validator...{Style.RESET_ALL}")

    # 创建必要的目录
    directories = ['config', 'logs', 'results', 'data']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        click.echo(f"✓ Created directory: {directory}")

    # 检查配置文件
    config_files = [
        'config/proxies.txt',
        'config/email_domains.json',
        'config/settings.json'
    ]

    for config_file in config_files:
        if Path(config_file).exists():
            click.echo(f"✓ Config file exists: {config_file}")
        else:
            click.echo(f"{Fore.YELLOW}⚠ Config file missing: {config_file}{Style.RESET_ALL}")

    # 创建示例邮箱文件
    sample_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]

    sample_file = Path("data/sample_emails.txt")
    with open(sample_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(sample_emails))

    click.echo(f"✓ Created sample email file: {sample_file}")
    click.echo(f"{Fore.GREEN}Setup completed!{Style.RESET_ALL}")

if __name__ == '__main__':
    cli()
