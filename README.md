# 高级邮件验证工具 - 日本运营商专版

这是一个专门针对日本三大运营商（DoCoMo、au/KDDI、SoftBank）的高级邮件验证工具，支持代理轮换、批量处理和详细报告。

## 🌟 主要功能

- ✅ **日本运营商特定验证** - 针对DoCoMo、au/KDDI、SoftBank的特殊规则
- 🔄 **智能代理轮换** - 支持代理池管理和自动故障转移
- 📊 **批量处理** - 高效的并发验证，支持大量邮箱
- 🎯 **多层验证** - 语法、MX记录、SMTP连接全方位检查
- 📈 **详细报告** - JSON格式输出，包含完整验证信息
- 🎨 **友好界面** - 彩色命令行界面，进度条显示

## 📋 系统要求

- Python 3.7+
- Windows/Linux/macOS
- 网络连接（用于DNS和SMTP验证）

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 初始化配置

```bash
python main.py setup
```

### 3. 配置代理（可选）

编辑 `config/proxies.txt` 文件，添加你的代理：

```
global.rotgb.711proxy.com:10000:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10001:USER456549-zone-custom:ec5512
```

### 4. 验证单个邮箱

```bash
python main.<NAME_EMAIL>
```

### 5. 批量验证

```bash
python main.py batch data/sample_emails.txt -o results.json
```

## 📖 详细使用说明

### 命令行选项

#### 验证单个邮箱
```bash
python main.py validate [EMAIL] [OPTIONS]
```

选项：
- `--use-proxy` - 使用代理进行验证
- `--proxy-file` - 指定代理文件路径（默认：config/proxies.txt）

示例：
```bash
python main.<NAME_EMAIL> --use-proxy
```

#### 批量验证
```bash
python main.py batch [INPUT_FILE] [OPTIONS]
```

选项：
- `-o, --output` - 输出文件名（默认：validation_results.json）
- `-w, --workers` - 并发线程数（默认：10）
- `--use-proxy` - 使用代理进行验证
- `--proxy-file` - 指定代理文件路径

示例：
```bash
python main.py batch emails.txt -o results.json -w 20 --use-proxy
```

#### 检查代理状态
```bash
python main.py proxy-status [OPTIONS]
```

选项：
- `--proxy-file` - 代理文件路径

#### 初始化设置
```bash
python main.py setup
```

## 📁 项目结构

```
email-validator/
├── main.py                 # 主程序入口
├── requirements.txt        # Python依赖
├── README.md              # 说明文档
├── .env.example           # 环境变量示例
├── config/                # 配置文件目录
│   ├── proxies.txt        # 代理配置
│   ├── email_domains.json # 邮箱域名配置
│   └── settings.json      # 全局设置
├── src/                   # 源代码目录
│   ├── __init__.py
│   ├── email_validator.py # 核心验证模块
│   ├── proxy_manager.py   # 代理管理模块
│   └── japan_carriers.py  # 日本运营商特定验证
├── logs/                  # 日志文件
├── results/               # 验证结果
└── data/                  # 数据文件
    └── sample_emails.txt  # 示例邮箱
```

## ⚙️ 配置说明

### 代理配置 (config/proxies.txt)

格式：`host:port:username:password`

```
# 示例代理配置
global.rotgb.711proxy.com:10000:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10001:USER456549-zone-custom:ec5512
```

### 邮箱域名配置 (config/email_domains.json)

包含日本三大运营商的域名、SMTP服务器和验证规则：

- **DoCoMo**: docomo.ne.jp
- **au/KDDI**: au.com, ezweb.ne.jp, uqmobile.jp  
- **SoftBank**: softbank.ne.jp, i.softbank.jp, vodafone.ne.jp, disney.ne.jp

### 全局设置 (config/settings.json)

- 并发设置
- 超时配置
- 验证选项
- 输出格式

## 📊 验证结果格式

验证结果以JSON格式保存，包含以下信息：

```json
{
  "email": "<EMAIL>",
  "is_valid": true,
  "result": "valid",
  "carrier": "docomo",
  "domain": "docomo.ne.jp",
  "syntax_valid": true,
  "mx_valid": true,
  "smtp_valid": true,
  "error_message": null,
  "response_time": 1.23
}
```

## 🎯 日本运营商特定验证

### DoCoMo (docomo.ne.jp)
- 最大长度：30字符
- 不能包含连续特殊字符
- 特殊的字符序列限制

### au/KDDI (au.com, ezweb.ne.jp)
- 最大长度：30字符
- 禁止连续特殊字符
- 保留词检查

### SoftBank (softbank.ne.jp, i.softbank.jp)
- 必须以字母开头
- 字母或数字结尾
- 点号数量限制

## 🔧 高级功能

### 代理健康检查
- 自动检测代理可用性
- 失败代理自动移除
- 响应时间监控

### 智能重试机制
- 网络错误自动重试
- 代理故障自动切换
- 指数退避算法

### 详细日志记录
- 验证过程完整记录
- 错误信息详细追踪
- 性能指标统计

## 🚨 注意事项

1. **合规使用** - 请确保遵守相关法律法规和服务条款
2. **频率限制** - 避免过于频繁的验证请求
3. **代理质量** - 使用高质量的代理服务以确保验证准确性
4. **网络环境** - 确保网络连接稳定

## 🐛 故障排除

### 常见问题

1. **代理连接失败**
   - 检查代理配置格式
   - 验证代理服务器可用性
   - 确认认证信息正确

2. **验证结果不准确**
   - 检查网络连接
   - 更新邮箱域名配置
   - 调整超时设置

3. **性能问题**
   - 减少并发线程数
   - 增加请求延迟
   - 优化代理配置

## 📞 技术支持

如果遇到问题或需要帮助，请：

1. 检查日志文件 `logs/email_validator.log`
2. 确认配置文件格式正确
3. 验证网络连接和代理设置

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
