# 🚀 批量验证快速开始

## 📝 第一步：准备邮箱列表文件

### 方法1：手动创建（最简单）

创建一个文本文件，比如 `my_emails.txt`：

```
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
```

**重要格式要求**：
- ✅ 每行一个邮箱地址
- ✅ 不要有多余的空格
- ✅ 保存为UTF-8编码
- ✅ 文件扩展名为 `.txt`

### 方法2：从Excel复制粘贴

1. 在Excel中选择邮箱列
2. 复制 (Ctrl+C)
3. 打开记事本
4. 粘贴 (Ctrl+V)
5. 保存为 `emails.txt`

### 方法3：使用工具生成示例

```bash
# 生成20个示例邮箱
python tools/email_list_manager.py sample 20 my_test_emails.txt
```

## 🔧 第二步：配置代理（重要）

编辑 `config/proxies.txt` 文件，添加你的代理：

```
global.rotgb.711proxy.com:10000:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10001:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10002:USER456549-zone-custom:ec5512
```

**格式说明**：`主机:端口:用户名:密码`

## 🚀 第三步：执行批量验证

### 基础命令

```bash
# 最简单的批量验证
python main.py batch my_emails.txt
```

### 推荐命令（使用代理）

```bash
# 使用代理进行批量验证
python main.py batch my_emails.txt --use-proxy
```

### 完整命令（所有选项）

```bash
# 完整的批量验证命令
python main.py batch my_emails.txt \
  --use-proxy \
  --output my_results.json \
  --workers 20
```

## 📊 第四步：查看验证结果

### 命令行输出示例

```
✓ Loaded 3/3 working proxies
Found 6 emails to validate
Validating emails: 100%|████████████| 6/6 [00:15<00:00, 2.5s/it]
✓ Results saved to: results/my_results.json

=== Validation Summary ===
Total emails: 6
Valid: 4
Invalid: 2
Errors: 0

=== Carrier Statistics ===
┌─────────┬───────┬───────┬──────────────┐
│ Carrier │ Total │ Valid │ Success Rate │
├─────────┼───────┼───────┼──────────────┤
│ DOCOMO  │   2   │   2   │    100.0%    │
│ AU_KDDI │   2   │   1   │     50.0%    │
│ SOFTBANK│   2   │   1   │     50.0%    │
└─────────┴───────┴───────┴──────────────┘
```

### 结果文件位置

验证结果保存在：`results/my_results.json`

## 📋 常用批量验证命令

### 小规模验证（< 100个邮箱）

```bash
python main.py batch small_list.txt --use-proxy -w 5
```

### 中等规模验证（100-1000个邮箱）

```bash
python main.py batch medium_list.txt --use-proxy -w 20
```

### 大规模验证（> 1000个邮箱）

```bash
python main.py batch large_list.txt --use-proxy -w 50
```

### 自定义输出文件名

```bash
python main.py batch emails.txt --use-proxy -o custom_results.json
```

## 🔍 结果分析

### 查看JSON结果文件

```bash
# Windows
notepad results/my_results.json

# 或者用任何文本编辑器打开
```

### 筛选有效邮箱

创建一个简单的Python脚本 `filter_valid.py`：

```python
import json

# 读取验证结果
with open('results/my_results.json', 'r', encoding='utf-8') as f:
    results = json.load(f)

# 筛选有效邮箱
valid_emails = []
for result in results:
    if result['is_valid']:
        valid_emails.append(result['email'])

# 保存有效邮箱
with open('valid_emails.txt', 'w', encoding='utf-8') as f:
    f.write('\n'.join(valid_emails))

print(f"总邮箱数: {len(results)}")
print(f"有效邮箱: {len(valid_emails)}")
print(f"成功率: {len(valid_emails)/len(results)*100:.1f}%")
print(f"有效邮箱已保存到: valid_emails.txt")
```

运行筛选脚本：

```bash
python filter_valid.py
```

## 🛠️ 实用工具命令

### 检查代理状态

```bash
python main.py proxy-status
```

### 创建示例邮箱列表

```bash
# 创建100个示例邮箱
python tools/email_list_manager.py sample 100 test_emails.txt
```

### 清理邮箱列表

```bash
# 去重和格式化邮箱列表
python tools/email_list_manager.py clean raw_emails.txt clean_emails.txt
```

### 从CSV提取邮箱

```bash
# 从CSV文件提取邮箱列
python tools/csv_to_emails.py data.csv email_column output_emails.txt
```

## ⚡ 快速测试流程

### 5分钟快速测试

```bash
# 1. 创建测试邮箱列表
echo "<EMAIL>
<EMAIL>
<EMAIL>" > quick_test.txt

# 2. 检查代理（确保已配置）
python main.py proxy-status

# 3. 快速验证
python main.py batch quick_test.txt --use-proxy

# 4. 查看结果
cat results/validation_results.json
```

## 🚨 注意事项

### 文件格式要求

- ✅ **正确格式**：
  ```
  <EMAIL>
  <EMAIL>
  ```

- ❌ **错误格式**：
  ```
  <EMAIL>, <EMAIL>  # 不要用逗号分隔
  "<EMAIL>"             # 不要用引号
  <EMAIL>;              # 不要有分号
  ```

### 性能建议

- 📊 **小文件** (< 100邮箱)：并发数 5-10
- 📊 **中等文件** (100-1000邮箱)：并发数 10-20  
- 📊 **大文件** (> 1000邮箱)：并发数 20-50

### 代理使用

- 🔄 **必须使用代理**：避免IP被封
- 🔄 **多个代理**：提高成功率
- 🔄 **定期检查**：确保代理可用

## 📞 遇到问题？

### 常见错误解决

1. **"文件不存在"**：
   - 检查文件路径是否正确
   - 确保文件在正确的目录

2. **"代理连接失败"**：
   - 检查 `config/proxies.txt` 格式
   - 运行 `python main.py proxy-status`

3. **"验证速度慢"**：
   - 减少并发数 `-w 5`
   - 检查网络连接

4. **"结果不准确"**：
   - 使用高质量代理
   - 降低验证频率

### 获取帮助

- 📖 查看详细文档：`README.md`
- 📖 安装指南：`INSTALL_GUIDE.md`
- 📖 使用示例：`USAGE_EXAMPLES.md`
- 📝 查看日志：`logs/email_validator.log`

---

🎉 **现在你可以开始批量验证邮箱了！记住先配置好代理，然后准备邮箱列表文件。**
