import re
import json
import requests
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
from enum import Enum

class JapanCarrier(Enum):
    DOCOMO = "docomo"
    AU_KDDI = "au_kddi"
    SOFTBANK = "softbank"
    UNKNOWN = "unknown"

@dataclass
class CarrierValidationResult:
    carrier: JapanCarrier
    is_valid: bool
    domain_valid: bool
    format_valid: bool
    carrier_specific_valid: bool
    error_message: Optional[str] = None

class JapanCarrierValidator:
    def __init__(self, config_path: str = "config/email_domains.json"):
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config(config_path)
        self.carrier_patterns = self._init_carrier_patterns()
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load config: {e}")
            return {}
    
    def _init_carrier_patterns(self) -> Dict[JapanCarrier, Dict]:
        """初始化运营商特定的验证模式"""
        return {
            JapanCarrier.DOCOMO: {
                'domains': ['docomo.ne.jp'],
                'local_pattern': r'^[a-zA-Z0-9][a-zA-Z0-9._-]{1,29}[a-zA-Z0-9]$',
                'forbidden_patterns': [r'\.\.', r'^\.', r'\.$'],
                'max_length': 30,
                'special_rules': ['no_consecutive_dots', 'alphanumeric_start_end']
            },
            JapanCarrier.AU_KDDI: {
                'domains': ['au.com', 'ezweb.ne.jp', 'uqmobile.jp'],
                'local_pattern': r'^[a-zA-Z0-9][a-zA-Z0-9._-]{1,29}[a-zA-Z0-9]$',
                'forbidden_patterns': [r'\.\.', r'^\.', r'\.$'],
                'max_length': 30,
                'special_rules': ['no_consecutive_dots', 'alphanumeric_start_end']
            },
            JapanCarrier.SOFTBANK: {
                'domains': ['softbank.ne.jp', 'i.softbank.jp', 'vodafone.ne.jp', 'disney.ne.jp'],
                'local_pattern': r'^[a-zA-Z0-9][a-zA-Z0-9._-]{1,29}[a-zA-Z0-9]$',
                'forbidden_patterns': [r'\.\.', r'^\.', r'\.$'],
                'max_length': 30,
                'special_rules': ['no_consecutive_dots', 'alphanumeric_start_end']
            }
        }
    
    def detect_carrier(self, email: str) -> JapanCarrier:
        """检测邮箱所属的日本运营商"""
        domain = email.split('@')[-1].lower()
        
        for carrier, patterns in self.carrier_patterns.items():
            if domain in patterns['domains']:
                return carrier
        
        return JapanCarrier.UNKNOWN
    
    def validate_docomo_specific(self, local_part: str) -> Tuple[bool, str]:
        """DoCoMo特定验证规则"""
        # DoCoMo特殊规则
        if len(local_part) > 30:
            return False, "DoCoMo email local part cannot exceed 30 characters"
        
        # 不能以数字开头（某些情况下）
        if local_part[0].isdigit() and len(local_part) < 3:
            return False, "DoCoMo email cannot start with digit if less than 3 characters"
        
        # 特殊字符限制
        forbidden_sequences = ['..', '.-', '-.', '_-', '-_']
        for seq in forbidden_sequences:
            if seq in local_part:
                return False, f"DoCoMo email cannot contain sequence: {seq}"
        
        return True, "Valid DoCoMo format"
    
    def validate_au_specific(self, local_part: str) -> Tuple[bool, str]:
        """au/KDDI特定验证规则"""
        # au特殊规则
        if len(local_part) > 30:
            return False, "au email local part cannot exceed 30 characters"
        
        # 连续特殊字符检查
        if re.search(r'[._-]{2,}', local_part):
            return False, "au email cannot contain consecutive special characters"
        
        # 特定禁用词检查
        forbidden_words = ['admin', 'root', 'test', 'mail']
        if any(word in local_part.lower() for word in forbidden_words):
            return False, "au email cannot contain reserved words"
        
        return True, "Valid au format"
    
    def validate_softbank_specific(self, local_part: str) -> Tuple[bool, str]:
        """SoftBank特定验证规则"""
        # SoftBank特殊规则
        if len(local_part) > 30:
            return False, "SoftBank email local part cannot exceed 30 characters"
        
        # i.softbank.jp的特殊规则
        if re.match(r'^[a-z][a-z0-9._-]*[a-z0-9]$', local_part):
            # 必须以字母开头，字母或数字结尾
            pass
        else:
            return False, "SoftBank email must start with letter and end with letter or number"
        
        # 特殊字符位置限制
        if local_part.count('.') > 3:
            return False, "SoftBank email cannot contain more than 3 dots"
        
        return True, "Valid SoftBank format"
    
    def validate_carrier_format(self, email: str) -> CarrierValidationResult:
        """验证运营商特定格式"""
        carrier = self.detect_carrier(email)
        local_part = email.split('@')[0]
        domain = email.split('@')[1].lower()
        
        result = CarrierValidationResult(
            carrier=carrier,
            is_valid=False,
            domain_valid=False,
            format_valid=False,
            carrier_specific_valid=False
        )
        
        if carrier == JapanCarrier.UNKNOWN:
            result.error_message = "Not a recognized Japanese carrier email"
            return result
        
        # 验证域名
        patterns = self.carrier_patterns[carrier]
        result.domain_valid = domain in patterns['domains']
        
        if not result.domain_valid:
            result.error_message = f"Invalid domain for {carrier.value}"
            return result
        
        # 基本格式验证
        pattern = patterns['local_pattern']
        result.format_valid = bool(re.match(pattern, local_part))
        
        if not result.format_valid:
            result.error_message = f"Invalid format for {carrier.value}"
            return result
        
        # 运营商特定验证
        if carrier == JapanCarrier.DOCOMO:
            result.carrier_specific_valid, error_msg = self.validate_docomo_specific(local_part)
        elif carrier == JapanCarrier.AU_KDDI:
            result.carrier_specific_valid, error_msg = self.validate_au_specific(local_part)
        elif carrier == JapanCarrier.SOFTBANK:
            result.carrier_specific_valid, error_msg = self.validate_softbank_specific(local_part)
        else:
            result.carrier_specific_valid = True
            error_msg = "No specific validation"
        
        if not result.carrier_specific_valid:
            result.error_message = error_msg
            return result
        
        # 所有验证通过
        result.is_valid = True
        return result
    
    def get_carrier_info(self, carrier: JapanCarrier) -> Dict:
        """获取运营商信息"""
        if carrier in self.carrier_patterns:
            return self.carrier_patterns[carrier]
        return {}
    
    def suggest_corrections(self, email: str) -> List[str]:
        """建议邮箱格式修正"""
        suggestions = []
        carrier = self.detect_carrier(email)
        
        if carrier == JapanCarrier.UNKNOWN:
            # 建议可能的域名
            suggestions.extend([
                email.replace('@', '@docomo.ne.jp').replace(email.split('@')[1], 'docomo.ne.jp'),
                email.replace('@', '@au.com').replace(email.split('@')[1], 'au.com'),
                email.replace('@', '@softbank.ne.jp').replace(email.split('@')[1], 'softbank.ne.jp')
            ])
        
        return suggestions[:3]  # 返回前3个建议
