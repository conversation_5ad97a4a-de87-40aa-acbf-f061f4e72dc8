import random
import time
import requests
import threading
from typing import List, Dict, Optional, Tuple
import logging
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import socket
import socks

@dataclass
class ProxyInfo:
    host: str
    port: int
    username: str
    password: str
    protocol: str = "http"
    failures: int = 0
    last_used: float = 0
    last_check: float = 0
    is_working: bool = True
    response_time: float = 0

class ProxyManager:
    def __init__(self, proxy_file: str = "config/proxies.txt", max_failures: int = 5):
        self.logger = logging.getLogger(__name__)
        self.proxy_file = proxy_file
        self.max_failures = max_failures
        self.proxies: List[ProxyInfo] = []
        self.current_index = 0
        self.lock = threading.Lock()
        self.health_check_interval = 300  # 5分钟
        self.last_health_check = 0
        
        self.load_proxies()
        
    def load_proxies(self):
        """从文件加载代理列表"""
        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            self.proxies = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split(':')
                    if len(parts) >= 4:
                        proxy = ProxyInfo(
                            host=parts[0],
                            port=int(parts[1]),
                            username=parts[2],
                            password=parts[3]
                        )
                        self.proxies.append(proxy)
            
            self.logger.info(f"Loaded {len(self.proxies)} proxies")
            
        except Exception as e:
            self.logger.error(f"Failed to load proxies: {e}")
            self.proxies = []
    
    def get_proxy_dict(self, proxy: ProxyInfo) -> Dict[str, str]:
        """获取代理字典格式"""
        auth = f"{proxy.username}:{proxy.password}"
        proxy_url = f"http://{auth}@{proxy.host}:{proxy.port}"
        
        return {
            'http': proxy_url,
            'https': proxy_url
        }
    
    def test_proxy(self, proxy: ProxyInfo, timeout: int = 10) -> Tuple[bool, float]:
        """测试代理连接"""
        try:
            start_time = time.time()
            proxy_dict = self.get_proxy_dict(proxy)
            
            # 测试HTTP连接
            response = requests.get(
                'http://httpbin.org/ip',
                proxies=proxy_dict,
                timeout=timeout,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                return True, response_time
            else:
                return False, response_time
                
        except Exception as e:
            self.logger.debug(f"Proxy test failed for {proxy.host}:{proxy.port} - {e}")
            return False, 0
    
    def health_check(self):
        """健康检查所有代理"""
        if time.time() - self.last_health_check < self.health_check_interval:
            return
        
        self.logger.info("Starting proxy health check...")
        
        def check_proxy(proxy):
            is_working, response_time = self.test_proxy(proxy)
            with self.lock:
                proxy.is_working = is_working
                proxy.response_time = response_time
                proxy.last_check = time.time()
                
                if not is_working:
                    proxy.failures += 1
                else:
                    proxy.failures = 0  # 重置失败计数
        
        # 并发检查所有代理
        with ThreadPoolExecutor(max_workers=10) as executor:
            executor.map(check_proxy, self.proxies)
        
        # 移除失败次数过多的代理
        working_proxies = [p for p in self.proxies if p.failures < self.max_failures]
        removed_count = len(self.proxies) - len(working_proxies)
        
        if removed_count > 0:
            self.logger.warning(f"Removed {removed_count} failed proxies")
            self.proxies = working_proxies
        
        self.last_health_check = time.time()
        working_count = sum(1 for p in self.proxies if p.is_working)
        self.logger.info(f"Health check completed. {working_count}/{len(self.proxies)} proxies working")
    
    def get_next_proxy(self) -> Optional[ProxyInfo]:
        """获取下一个可用代理"""
        if not self.proxies:
            return None
        
        # 执行健康检查
        self.health_check()
        
        with self.lock:
            # 过滤可用代理
            working_proxies = [p for p in self.proxies if p.is_working]
            
            if not working_proxies:
                self.logger.warning("No working proxies available")
                return None
            
            # 轮换策略：选择最少使用的代理
            proxy = min(working_proxies, key=lambda p: p.last_used)
            proxy.last_used = time.time()
            
            return proxy
    
    def get_random_proxy(self) -> Optional[ProxyInfo]:
        """获取随机代理"""
        working_proxies = [p for p in self.proxies if p.is_working]
        if working_proxies:
            return random.choice(working_proxies)
        return None
    
    def mark_proxy_failed(self, proxy: ProxyInfo):
        """标记代理失败"""
        with self.lock:
            proxy.failures += 1
            if proxy.failures >= self.max_failures:
                proxy.is_working = False
                self.logger.warning(f"Proxy {proxy.host}:{proxy.port} marked as failed")
    
    def get_proxy_stats(self) -> Dict:
        """获取代理统计信息"""
        total = len(self.proxies)
        working = sum(1 for p in self.proxies if p.is_working)
        avg_response_time = sum(p.response_time for p in self.proxies if p.response_time > 0) / max(1, total)
        
        return {
            'total_proxies': total,
            'working_proxies': working,
            'failed_proxies': total - working,
            'average_response_time': avg_response_time,
            'last_health_check': self.last_health_check
        }
    
    def create_socks_proxy(self, proxy: ProxyInfo) -> socket.socket:
        """创建SOCKS代理连接"""
        sock = socks.socksocket()
        sock.set_proxy(socks.HTTP, proxy.host, proxy.port, username=proxy.username, password=proxy.password)
        return sock
