#!/usr/bin/env python3
"""
邮箱列表管理工具
支持创建、合并、清理邮箱列表
"""

import re
import sys
from pathlib import Path
from typing import List, Set

class EmailListManager:
    def __init__(self):
        self.email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        
    def validate_email_format(self, email: str) -> bool:
        """验证邮箱格式"""
        return bool(self.email_pattern.match(email.strip()))
    
    def load_emails_from_file(self, file_path: str) -> List[str]:
        """从文件加载邮箱列表"""
        emails = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # 跳过空行和注释
                    if not line or line.startswith('#'):
                        continue
                    
                    # 处理可能包含其他信息的行
                    # 例如: "用户名,<EMAIL>,其他信息"
                    parts = line.split(',')
                    for part in parts:
                        part = part.strip()
                        if '@' in part and self.validate_email_format(part):
                            emails.append(part)
                            break
                    else:
                        # 如果没有找到有效邮箱，尝试整行
                        if '@' in line and self.validate_email_format(line):
                            emails.append(line)
                        else:
                            print(f"警告: 第{line_num}行格式无效: {line}")
        
        except Exception as e:
            print(f"读取文件失败: {e}")
            return []
        
        return emails
    
    def save_emails_to_file(self, emails: List[str], file_path: str) -> bool:
        """保存邮箱列表到文件"""
        try:
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(emails))
            
            return True
        except Exception as e:
            print(f"保存文件失败: {e}")
            return False
    
    def clean_email_list(self, emails: List[str]) -> List[str]:
        """清理邮箱列表"""
        cleaned = []
        seen = set()
        
        for email in emails:
            email = email.strip().lower()
            
            # 验证格式
            if not self.validate_email_format(email):
                continue
            
            # 去重
            if email in seen:
                continue
            
            seen.add(email)
            cleaned.append(email)
        
        return cleaned
    
    def filter_japan_carriers(self, emails: List[str]) -> List[str]:
        """筛选日本运营商邮箱"""
        japan_domains = [
            'docomo.ne.jp',
            'au.com',
            'ezweb.ne.jp',
            'uqmobile.jp',
            'softbank.ne.jp',
            'i.softbank.jp',
            'vodafone.ne.jp',
            'disney.ne.jp'
        ]
        
        filtered = []
        for email in emails:
            domain = email.split('@')[-1].lower()
            if domain in japan_domains:
                filtered.append(email)
        
        return filtered
    
    def merge_email_lists(self, file_paths: List[str]) -> List[str]:
        """合并多个邮箱列表文件"""
        all_emails = []
        
        for file_path in file_paths:
            if Path(file_path).exists():
                emails = self.load_emails_from_file(file_path)
                all_emails.extend(emails)
                print(f"✓ 从 {file_path} 加载了 {len(emails)} 个邮箱")
            else:
                print(f"✗ 文件不存在: {file_path}")
        
        return all_emails
    
    def split_email_list(self, emails: List[str], chunk_size: int) -> List[List[str]]:
        """将邮箱列表分割成小块"""
        chunks = []
        for i in range(0, len(emails), chunk_size):
            chunks.append(emails[i:i + chunk_size])
        return chunks
    
    def create_sample_list(self, output_file: str, count: int = 50):
        """创建示例邮箱列表"""
        domains = [
            'docomo.ne.jp',
            'au.com',
            'ezweb.ne.jp',
            'softbank.ne.jp',
            'i.softbank.jp',
            'uqmobile.jp'
        ]
        
        emails = []
        for i in range(count):
            domain = domains[i % len(domains)]
            email = f"user{i+1}@{domain}"
            emails.append(email)
        
        if self.save_emails_to_file(emails, output_file):
            print(f"✓ 创建了包含 {count} 个邮箱的示例文件: {output_file}")
        
    def interactive_mode(self):
        """交互式模式"""
        print("=== 邮箱列表管理工具 ===")
        print("1. 创建示例邮箱列表")
        print("2. 清理邮箱列表")
        print("3. 合并多个邮箱列表")
        print("4. 筛选日本运营商邮箱")
        print("5. 分割大型邮箱列表")
        print("0. 退出")
        
        while True:
            choice = input("\n请选择操作 (0-5): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                output_file = input("输出文件名 (默认: sample_emails.txt): ").strip()
                if not output_file:
                    output_file = "sample_emails.txt"
                
                count = input("邮箱数量 (默认: 50): ").strip()
                try:
                    count = int(count) if count else 50
                except ValueError:
                    count = 50
                
                self.create_sample_list(output_file, count)
                
            elif choice == '2':
                input_file = input("输入文件路径: ").strip()
                output_file = input("输出文件路径: ").strip()
                
                emails = self.load_emails_from_file(input_file)
                if emails:
                    cleaned = self.clean_email_list(emails)
                    if self.save_emails_to_file(cleaned, output_file):
                        print(f"✓ 清理完成: {len(emails)} -> {len(cleaned)} 个邮箱")
                
            elif choice == '3':
                file_paths = input("输入文件路径 (用空格分隔): ").strip().split()
                output_file = input("输出文件路径: ").strip()
                
                all_emails = self.merge_email_lists(file_paths)
                cleaned = self.clean_email_list(all_emails)
                
                if self.save_emails_to_file(cleaned, output_file):
                    print(f"✓ 合并完成: 总共 {len(cleaned)} 个唯一邮箱")
                
            elif choice == '4':
                input_file = input("输入文件路径: ").strip()
                output_file = input("输出文件路径: ").strip()
                
                emails = self.load_emails_from_file(input_file)
                if emails:
                    filtered = self.filter_japan_carriers(emails)
                    if self.save_emails_to_file(filtered, output_file):
                        print(f"✓ 筛选完成: {len(emails)} -> {len(filtered)} 个日本运营商邮箱")
                
            elif choice == '5':
                input_file = input("输入文件路径: ").strip()
                chunk_size = input("每个文件的邮箱数量 (默认: 1000): ").strip()
                try:
                    chunk_size = int(chunk_size) if chunk_size else 1000
                except ValueError:
                    chunk_size = 1000
                
                emails = self.load_emails_from_file(input_file)
                if emails:
                    chunks = self.split_email_list(emails, chunk_size)
                    
                    base_name = Path(input_file).stem
                    for i, chunk in enumerate(chunks, 1):
                        output_file = f"{base_name}_part{i}.txt"
                        if self.save_emails_to_file(chunk, output_file):
                            print(f"✓ 创建文件: {output_file} ({len(chunk)} 个邮箱)")
            
            else:
                print("无效选择，请重试")

def main():
    """主函数"""
    manager = EmailListManager()
    
    if len(sys.argv) == 1:
        # 交互式模式
        manager.interactive_mode()
    else:
        # 命令行模式
        if sys.argv[1] == 'clean':
            if len(sys.argv) != 4:
                print("使用方法: python email_list_manager.py clean <输入文件> <输出文件>")
                return
            
            emails = manager.load_emails_from_file(sys.argv[2])
            cleaned = manager.clean_email_list(emails)
            
            if manager.save_emails_to_file(cleaned, sys.argv[3]):
                print(f"✓ 清理完成: {len(emails)} -> {len(cleaned)} 个邮箱")
        
        elif sys.argv[1] == 'sample':
            count = int(sys.argv[2]) if len(sys.argv) > 2 else 50
            output_file = sys.argv[3] if len(sys.argv) > 3 else "sample_emails.txt"
            manager.create_sample_list(output_file, count)
        
        else:
            print("使用方法:")
            print("python email_list_manager.py                    # 交互式模式")
            print("python email_list_manager.py clean <输入> <输出>  # 清理邮箱列表")
            print("python email_list_manager.py sample [数量] [文件] # 创建示例列表")

if __name__ == "__main__":
    main()
