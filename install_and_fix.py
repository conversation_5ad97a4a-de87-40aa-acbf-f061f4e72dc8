#!/usr/bin/env python3
"""
安装和修复脚本
自动安装依赖并修复常见问题
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def install_dependencies():
    """安装必需的依赖包"""
    print("🔧 安装Python依赖包...")
    
    # 基础依赖包
    basic_packages = [
        "requests",
        "colorama", 
        "tqdm",
        "click",
        "tabulate"
    ]
    
    for package in basic_packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {package} 安装失败")
            return False
    
    return True

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建项目目录...")
    
    directories = ["config", "logs", "results", "data", "tools"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ 创建目录: {directory}")

def create_config_files():
    """创建配置文件"""
    print("\n⚙️ 创建配置文件...")
    
    # 代理配置文件
    proxy_config = """# 代理配置文件
# 格式: host:port:username:password
# 请将下面的示例替换为你的实际代理信息
global.rotgb.711proxy.com:10000:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10001:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10002:USER456549-zone-custom:ec5512

# 添加更多代理...
"""
    
    with open("config/proxies.txt", "w", encoding="utf-8") as f:
        f.write(proxy_config)
    print("✅ 创建代理配置文件")
    
    # 邮箱域名配置
    email_domains = {
        "japan_carriers": {
            "docomo": {
                "domains": ["docomo.ne.jp"],
                "smtp_servers": ["mail.docomo.ne.jp"]
            },
            "au_kddi": {
                "domains": ["au.com", "ezweb.ne.jp", "uqmobile.jp"],
                "smtp_servers": ["mail.au.com"]
            },
            "softbank": {
                "domains": ["softbank.ne.jp", "i.softbank.jp", "vodafone.ne.jp", "disney.ne.jp"],
                "smtp_servers": ["mail.softbank.ne.jp"]
            }
        }
    }
    
    with open("config/email_domains.json", "w", encoding="utf-8") as f:
        json.dump(email_domains, f, indent=2, ensure_ascii=False)
    print("✅ 创建邮箱域名配置文件")
    
    # 全局设置
    settings = {
        "general": {
            "max_concurrent_checks": 20,
            "request_delay": 0.5,
            "retry_attempts": 3,
            "timeout": 30
        },
        "proxy": {
            "enabled": True,
            "rotation_interval": 10,
            "max_failures": 5
        },
        "validation": {
            "check_syntax": True,
            "check_mx_record": True,
            "check_smtp": False
        }
    }
    
    with open("config/settings.json", "w", encoding="utf-8") as f:
        json.dump(settings, f, indent=2, ensure_ascii=False)
    print("✅ 创建全局设置文件")

def create_sample_email_file():
    """创建示例邮箱文件"""
    print("\n📧 创建示例邮箱文件...")
    
    sample_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    with open("data/sample_emails.txt", "w", encoding="utf-8") as f:
        f.write('\n'.join(sample_emails))
    
    print("✅ 创建示例邮箱文件: data/sample_emails.txt")

def test_installation():
    """测试安装是否成功"""
    print("\n🧪 测试安装...")
    
    try:
        # 测试导入
        sys.path.append("src")
        from email_validator import JapanEmailValidator
        
        validator = JapanEmailValidator()
        print("✅ 邮箱验证模块导入成功")
        
        # 测试基本功能
        test_email = "<EMAIL>"
        result = validator.validate_syntax(test_email)
        print(f"✅ 语法验证测试: {test_email} -> {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_batch_script():
    """创建批量验证脚本"""
    print("\n📝 创建批量验证脚本...")
    
    batch_script = '''#!/usr/bin/env python3
"""
简单批量验证脚本
"""

import sys
import os
sys.path.append("src")

from email_validator import JapanEmailValidator
import json
import time
from pathlib import Path

def batch_validate(email_file):
    """批量验证邮箱"""
    # 读取邮箱列表
    try:
        with open(email_file, 'r', encoding='utf-8') as f:
            emails = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {email_file}")
        return
    
    print(f"📧 开始验证 {len(emails)} 个邮箱地址...")
    
    validator = JapanEmailValidator()
    results = []
    valid_count = 0
    
    for i, email in enumerate(emails, 1):
        print(f"验证 {i:3d}/{len(emails)}: {email:<30}", end=" ")
        
        result = validator.validate_email(email)
        results.append({
            'email': email,
            'is_valid': result.is_valid,
            'carrier': result.carrier,
            'error_message': result.error_message,
            'response_time': result.response_time
        })
        
        if result.is_valid:
            print("✅ 有效")
            valid_count += 1
        else:
            print(f"❌ 无效 - {result.error_message}")
    
    # 显示统计
    print(f"\\n📊 验证完成!")
    print(f"总邮箱数: {len(emails)}")
    print(f"有效邮箱: {valid_count}")
    print(f"成功率: {(valid_count/len(emails)*100):.1f}%")
    
    # 保存结果
    Path("results").mkdir(exist_ok=True)
    output_file = f"results/batch_results_{int(time.time())}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"💾 结果已保存到: {output_file}")
    
    # 保存有效邮箱
    valid_emails = [r['email'] for r in results if r['is_valid']]
    if valid_emails:
        valid_file = "results/valid_emails.txt"
        with open(valid_file, 'w', encoding='utf-8') as f:
            f.write('\\n'.join(valid_emails))
        print(f"✅ 有效邮箱列表: {valid_file}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("使用方法: python batch_validator.py <邮箱文件>")
        print("示例: python batch_validator.py data/sample_emails.txt")
        sys.exit(1)
    
    email_file = sys.argv[1]
    if not os.path.exists(email_file):
        print(f"❌ 文件不存在: {email_file}")
        sys.exit(1)
    
    batch_validate(email_file)
'''
    
    with open("batch_validator.py", "w", encoding="utf-8") as f:
        f.write(batch_script)
    
    print("✅ 创建批量验证脚本: batch_validator.py")

def main():
    """主安装函数"""
    print("🎯 高级邮箱验证工具 - 安装和修复脚本")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 错误: 需要Python 3.6或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败")
        return False
    
    # 创建目录
    create_directories()
    
    # 创建配置文件
    create_config_files()
    
    # 创建示例文件
    create_sample_email_file()
    
    # 创建批量验证脚本
    create_batch_script()
    
    # 测试安装
    if test_installation():
        print("\n🎉 安装和修复完成!")
        print("\n📋 下一步:")
        print("1. 编辑 config/proxies.txt 添加你的代理信息")
        print("2. 准备你的邮箱列表文件")
        print("3. 运行批量验证:")
        print("   python batch_validator.py data/sample_emails.txt")
        print("   python batch_validator.py your_emails.txt")
        
        return True
    else:
        print("\n❌ 安装测试失败")
        return False

if __name__ == "__main__":
    main()
'''
    
    with open("batch_validator.py", "w", encoding="utf-8") as f:
        f.write(batch_script)
    
    print("✅ 创建批量验证脚本: batch_validator.py")

def main():
    """主安装函数"""
    print("🎯 高级邮箱验证工具 - 安装和修复脚本")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 错误: 需要Python 3.6或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败")
        return False
    
    # 创建目录
    create_directories()
    
    # 创建配置文件
    create_config_files()
    
    # 创建示例文件
    create_sample_email_file()
    
    # 创建批量验证脚本
    create_batch_script()
    
    # 测试安装
    if test_installation():
        print("\n🎉 安装和修复完成!")
        print("\n📋 下一步:")
        print("1. 编辑 config/proxies.txt 添加你的代理信息")
        print("2. 准备你的邮箱列表文件")
        print("3. 运行批量验证:")
        print("   python batch_validator.py data/sample_emails.txt")
        print("   python batch_validator.py your_emails.txt")
        
        return True
    else:
        print("\n❌ 安装测试失败")
        return False

if __name__ == "__main__":
    main()
