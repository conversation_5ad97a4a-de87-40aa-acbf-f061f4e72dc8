#!/usr/bin/env python3
"""
创建最终项目包
"""

import os
import zipfile
import shutil
from pathlib import Path
import datetime

def create_final_package():
    """创建最终项目压缩包"""
    
    project_name = "japan-email-validator-fixed"
    version = datetime.datetime.now().strftime("%Y%m%d_%H%M")
    package_name = f"{project_name}-v{version}.zip"
    
    print(f"🎯 创建最终项目包: {package_name}")
    
    # 需要包含的文件
    files_to_include = [
        # 主要脚本
        "batch_validator.py",
        "quick_install.py",
        
        # 配置文件
        "config/proxies.txt",
        
        # 示例数据
        "data/sample_emails.txt",
        
        # 原始源代码（可选）
        "src/email_validator.py",
        "src/proxy_manager.py",
        "src/japan_carriers.py",
        "src/__init__.py",
        "main.py",
        "requirements.txt"
    ]
    
    # 创建临时目录
    temp_dir = Path("temp_final_package")
    if temp_dir.exists():
        shutil.rmtree(temp_dir)
    temp_dir.mkdir()
    
    # 复制文件
    for file_path in files_to_include:
        src_path = Path(file_path)
        if src_path.exists():
            dst_path = temp_dir / file_path
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(src_path, dst_path)
            print(f"✅ 已添加: {file_path}")
        else:
            print(f"⚠️  文件不存在: {file_path}")
    
    # 创建必要的空目录
    empty_dirs = ["logs", "results"]
    for dir_name in empty_dirs:
        dir_path = temp_dir / dir_name
        dir_path.mkdir(exist_ok=True)
        gitkeep_file = dir_path / ".gitkeep"
        gitkeep_file.write_text("# 保持目录结构\n")
        print(f"✅ 已创建目录: {dir_name}")
    
    # 创建使用说明文件
    usage_guide = f"""# 日本邮箱验证工具 - 使用指南

版本: v{version}
创建时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 🚀 快速开始

### 1. 安装依赖（首次使用）
```bash
python quick_install.py
```

### 2. 配置代理
编辑 `config/proxies.txt` 文件，添加你的代理信息：
```
global.rotgb.711proxy.com:10000:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10001:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10002:USER456549-zone-custom:ec5512
```

### 3. 准备邮箱列表
创建一个文本文件，每行一个邮箱地址：
```
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
```

### 4. 执行批量验证
```bash
# 验证示例文件
python batch_validator.py data/sample_emails.txt

# 验证你的邮箱文件
python batch_validator.py your_emails.txt
```

## 📊 验证结果

验证完成后会显示：
- 总邮箱数量
- 有效/无效邮箱统计
- 按运营商分类统计
- 成功率

结果文件保存在 `results/` 目录：
- `batch_results_*.json` - 详细验证结果
- `valid_emails.txt` - 有效邮箱列表

## 🎯 支持的日本运营商

- **DoCoMo**: docomo.ne.jp
- **au/KDDI**: au.com, ezweb.ne.jp, uqmobile.jp
- **SoftBank**: softbank.ne.jp, i.softbank.jp, vodafone.ne.jp, disney.ne.jp

## 📁 文件说明

- `batch_validator.py` - 主要的批量验证脚本
- `quick_install.py` - 快速安装脚本
- `config/proxies.txt` - 代理配置文件
- `data/sample_emails.txt` - 示例邮箱文件
- `results/` - 验证结果目录
- `logs/` - 日志文件目录

## 🔧 故障排除

### 问题1: 依赖包安装失败
```bash
pip install requests colorama tqdm click tabulate
```

### 问题2: 邮箱文件格式错误
确保：
- 每行一个邮箱地址
- 使用UTF-8编码
- 没有多余的空格

### 问题3: 代理连接问题
- 检查代理格式：host:port:username:password
- 确认代理服务器可用
- 验证用户名密码正确

## 📞 技术支持

如果遇到问题：
1. 检查Python版本（需要3.6+）
2. 确认所有依赖包已安装
3. 验证文件格式和路径
4. 检查网络连接

祝使用愉快！
"""
    
    usage_file = temp_dir / "使用说明.txt"
    usage_file.write_text(usage_guide, encoding='utf-8')
    print("✅ 已创建使用说明文件")
    
    # 创建ZIP压缩包
    with zipfile.ZipFile(package_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                file_path = Path(root) / file
                arcname = file_path.relative_to(temp_dir)
                zipf.write(file_path, arcname)
                print(f"✅ 已压缩: {arcname}")
    
    # 清理临时目录
    shutil.rmtree(temp_dir)
    
    # 显示结果
    package_size = os.path.getsize(package_name) / 1024  # KB
    print(f"\n🎉 最终项目包创建成功!")
    print(f"📦 文件名: {package_name}")
    print(f"📏 大小: {package_size:.1f} KB")
    print(f"📍 位置: {os.path.abspath(package_name)}")
    
    return package_name

def create_download_instructions():
    """创建下载说明"""
    instructions = f"""
# 📥 日本邮箱验证工具 - 下载和使用指南

## 🎯 工具简介

这是一个专门针对日本三大运营商（DoCoMo、au/KDDI、SoftBank）的高级邮箱验证工具，支持：
- 批量验证邮箱地址
- 代理轮换支持
- 运营商特定验证规则
- 详细验证报告

## 📦 下载文件

下载最新的项目包：`japan-email-validator-fixed-v{datetime.datetime.now().strftime("%Y%m%d_%H%M")}.zip`

## 🚀 使用步骤

### 第一步：解压文件
将下载的ZIP文件解压到你的工作目录。

### 第二步：安装依赖
```bash
python quick_install.py
```

### 第三步：配置代理
编辑 `config/proxies.txt`，添加你的代理信息：
```
global.rotgb.711proxy.com:10000:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10001:USER456549-zone-custom:ec5512
```

### 第四步：准备邮箱列表
创建一个文本文件，每行一个邮箱：
```
<EMAIL>
<EMAIL>
<EMAIL>
```

### 第五步：执行验证
```bash
python batch_validator.py your_emails.txt
```

## 📊 验证结果示例

```
📧 开始验证 5 个邮箱地址...
验证   1/5: <EMAIL>     ✅ 有效
验证   2/5: <EMAIL>           ✅ 有效
验证   3/5: <EMAIL> ❌ 无效

📊 验证结果统计:
总邮箱数: 5
有效邮箱: 3
成功率: 60.0%

📱 运营商统计:
DoCoMo    :   1/  1 (100.0%)
au/KDDI   :   2/  2 (100.0%)
SoftBank  :   0/  2 (  0.0%)
```

## 🎯 支持的邮箱类型

- **DoCoMo**: docomo.ne.jp
- **au/KDDI**: au.com, ezweb.ne.jp, uqmobile.jp  
- **SoftBank**: softbank.ne.jp, i.softbank.jp, vodafone.ne.jp, disney.ne.jp

## 📁 项目结构

```
japan-email-validator/
├── batch_validator.py     # 主验证脚本
├── quick_install.py       # 安装脚本
├── config/
│   └── proxies.txt        # 代理配置
├── data/
│   └── sample_emails.txt  # 示例邮箱
├── results/               # 验证结果
└── 使用说明.txt           # 详细说明
```

## 🔧 常见问题

**Q: 如何添加更多代理？**
A: 编辑 `config/proxies.txt`，每行添加一个代理，格式：`host:port:username:password`

**Q: 支持多大的邮箱列表？**
A: 理论上无限制，建议单次验证不超过10000个邮箱

**Q: 验证速度如何？**
A: 取决于网络和代理质量，通常每秒可验证5-20个邮箱

**Q: 结果保存在哪里？**
A: `results/` 目录下，包含JSON详细结果和TXT有效邮箱列表

## 📞 技术支持

如果遇到问题：
1. 确保Python版本3.6+
2. 检查网络连接
3. 验证代理配置
4. 确认文件格式正确

---
🎉 **开始验证你的日本邮箱列表吧！**
"""
    
    with open("下载使用指南.md", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ 已创建下载使用指南")

def main():
    """主函数"""
    print("🎯 创建最终项目包")
    print("=" * 50)
    
    try:
        # 创建项目包
        package_name = create_final_package()
        
        # 创建下载说明
        create_download_instructions()
        
        print("\n" + "=" * 50)
        print("✅ 打包完成!")
        print(f"\n📦 你的最终项目包: {package_name}")
        print("📖 下载说明: 下载使用指南.md")
        
        print("\n🎯 项目包包含:")
        print("- batch_validator.py (主验证脚本)")
        print("- quick_install.py (安装脚本)")
        print("- config/proxies.txt (代理配置)")
        print("- data/sample_emails.txt (示例邮箱)")
        print("- 使用说明.txt (详细指南)")
        
        print("\n🚀 使用方法:")
        print("1. 下载并解压项目包")
        print("2. 运行: python quick_install.py")
        print("3. 配置代理: 编辑 config/proxies.txt")
        print("4. 验证邮箱: python batch_validator.py your_emails.txt")
        
    except Exception as e:
        print(f"❌ 打包失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
