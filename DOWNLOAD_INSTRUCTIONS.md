
# 📥 项目下载说明

## 方法1: 直接下载压缩包

运行打包脚本创建压缩包:
```bash
python create_package.py
```

然后下载生成的 .zip 文件。

## 方法2: 手动创建项目结构

如果无法下载压缩包，可以手动创建以下目录结构:

```
email-validator/
├── main.py
├── requirements.txt
├── README.md
├── INSTALL_GUIDE.md
├── USAGE_EXAMPLES.md
├── run_example.py
├── .env.example
├── config/
│   ├── proxies.txt
│   ├── email_domains.json
│   └── settings.json
├── src/
│   ├── __init__.py
│   ├── email_validator.py
│   ├── proxy_manager.py
│   └── japan_carriers.py
├── logs/
├── results/
└── data/
```

然后逐个复制文件内容。

## 方法3: 使用Git (如果有仓库)

```bash
git clone <repository-url>
cd email-validator
```

## 安装和使用

1. 解压或创建项目目录
2. 进入项目目录: `cd email-validator`
3. 安装依赖: `pip install -r requirements.txt`
4. 初始化配置: `python main.py setup`
5. 配置代理: 编辑 `config/proxies.txt`
6. 开始使用: `python main.<NAME_EMAIL> --use-proxy`

## 重要配置

### 代理配置 (config/proxies.txt)
```
global.rotgb.711proxy.com:10000:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10001:USER456549-zone-custom:ec5512
global.rotgb.711proxy.com:10002:USER456549-zone-custom:ec5512
```

### 环境变量 (.env)
复制 .env.example 为 .env 并根据需要修改。

如有问题，请参考 INSTALL_GUIDE.md 详细安装指南。
