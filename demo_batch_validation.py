#!/usr/bin/env python3
"""
批量验证演示脚本
展示如何使用高级邮箱验证软件进行批量验证
"""

import os
import sys
import json
from pathlib import Path

def create_sample_email_list():
    """创建示例邮箱列表"""
    sample_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    # 创建邮箱列表文件
    email_file = "demo_emails.txt"
    with open(email_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(sample_emails))
    
    print(f"✓ 创建示例邮箱列表: {email_file}")
    print(f"✓ 包含 {len(sample_emails)} 个邮箱地址")
    
    # 显示文件内容
    print(f"\n📄 邮箱列表内容:")
    for i, email in enumerate(sample_emails, 1):
        print(f"  {i:2d}. {email}")
    
    return email_file

def check_configuration():
    """检查配置文件"""
    print("\n🔧 检查配置文件...")
    
    config_files = {
        "config/proxies.txt": "代理配置",
        "config/email_domains.json": "邮箱域名配置", 
        "config/settings.json": "全局设置"
    }
    
    all_good = True
    for file_path, description in config_files.items():
        if os.path.exists(file_path):
            print(f"✓ {description}: {file_path}")
        else:
            print(f"✗ {description}: {file_path} (缺失)")
            all_good = False
    
    return all_good

def show_batch_commands(email_file):
    """显示批量验证命令"""
    print(f"\n🚀 批量验证命令:")
    print(f"=" * 50)
    
    print(f"\n1️⃣ 基础批量验证:")
    print(f"python main.py batch {email_file}")
    
    print(f"\n2️⃣ 使用代理验证 (推荐):")
    print(f"python main.py batch {email_file} --use-proxy")
    
    print(f"\n3️⃣ 完整配置验证:")
    print(f"python main.py batch {email_file} --use-proxy --output demo_results.json --workers 10")
    
    print(f"\n4️⃣ 高并发验证 (大量邮箱):")
    print(f"python main.py batch {email_file} --use-proxy --workers 30")

def show_proxy_setup():
    """显示代理设置说明"""
    print(f"\n🔄 代理配置说明:")
    print(f"=" * 50)
    
    print(f"\n📝 编辑文件: config/proxies.txt")
    print(f"格式: host:port:username:password")
    print(f"\n示例配置:")
    print(f"global.rotgb.711proxy.com:10000:USER456549-zone-custom:ec5512")
    print(f"global.rotgb.711proxy.com:10001:USER456549-zone-custom:ec5512")
    print(f"global.rotgb.711proxy.com:10002:USER456549-zone-custom:ec5512")
    
    print(f"\n🔍 检查代理状态:")
    print(f"python main.py proxy-status")

def show_result_analysis():
    """显示结果分析方法"""
    print(f"\n📊 结果分析:")
    print(f"=" * 50)
    
    print(f"\n1️⃣ 查看验证结果文件:")
    print(f"notepad results/demo_results.json")
    
    print(f"\n2️⃣ 筛选有效邮箱 (Python脚本):")
    filter_script = '''
import json

# 读取验证结果
with open('results/demo_results.json', 'r', encoding='utf-8') as f:
    results = json.load(f)

# 筛选有效邮箱
valid_emails = [r['email'] for r in results if r['is_valid']]

# 保存有效邮箱
with open('valid_emails.txt', 'w', encoding='utf-8') as f:
    f.write('\\n'.join(valid_emails))

print(f"总邮箱: {len(results)}")
print(f"有效邮箱: {len(valid_emails)}")
print(f"成功率: {len(valid_emails)/len(results)*100:.1f}%")
'''
    
    print(filter_script)
    
    print(f"\n3️⃣ 按运营商分类:")
    classify_script = '''
# 按运营商分组
carriers = {}
for result in results:
    carrier = result['carrier'] or 'unknown'
    if carrier not in carriers:
        carriers[carrier] = []
    carriers[carrier].append(result['email'])

# 保存分类结果
for carrier, emails in carriers.items():
    filename = f'{carrier}_emails.txt'
    with open(filename, 'w', encoding='utf-8') as f:
        f.write('\\n'.join(emails))
    print(f"{carrier}: {len(emails)} 个邮箱")
'''
    print(classify_script)

def main():
    """主演示函数"""
    print("🎯 高级邮箱验证软件 - 批量验证演示")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists("main.py"):
        print("❌ 错误: 请在邮箱验证软件的根目录运行此脚本")
        print("   确保当前目录包含 main.py 文件")
        return
    
    # 检查配置
    config_ok = check_configuration()
    
    # 创建示例邮箱列表
    email_file = create_sample_email_list()
    
    # 显示代理设置
    show_proxy_setup()
    
    # 显示批量验证命令
    show_batch_commands(email_file)
    
    # 显示结果分析方法
    show_result_analysis()
    
    print(f"\n" + "=" * 60)
    print(f"🎉 演示完成!")
    
    if config_ok:
        print(f"\n✅ 配置文件检查通过")
        print(f"📝 下一步:")
        print(f"1. 配置你的代理信息到 config/proxies.txt")
        print(f"2. 准备你的邮箱列表文件")
        print(f"3. 运行批量验证命令")
        print(f"\n🚀 快速测试命令:")
        print(f"python main.py batch {email_file} --use-proxy")
    else:
        print(f"\n⚠️  配置文件缺失")
        print(f"请先运行: python main.py setup")
    
    print(f"\n📞 需要帮助?")
    print(f"- 查看详细文档: README.md")
    print(f"- 安装指南: INSTALL_GUIDE.md") 
    print(f"- 批量验证指南: BATCH_VALIDATION_GUIDE.md")

if __name__ == "__main__":
    main()
